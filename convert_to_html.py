#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Markdown测试用例文档转换为HTML文档，便于转换为Word
"""

import re

def parse_markdown_file(file_path):
    """解析Markdown文件，提取测试用例"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取测试用例
    test_cases = []
    
    # 使用正则表达式匹配测试用例
    pattern = r'\*\*ID: (\d+)\*\*\n模块：([^\n]+)\n用例标题：([^\n]+)\n操作步骤：([^\n]+)\n预期结果：([^\n]+)\n备注：([^\n]*)'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        test_case = {
            'id': match[0],
            'module': match[1],
            'title': match[2],
            'steps': match[3],
            'expected': match[4],
            'remark': match[5] if match[5] else '（留空）'
        }
        test_cases.append(test_case)
    
    return test_cases

def create_html_document(test_cases):
    """创建HTML文档"""
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AFTN服务器系统测试用例</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
        }}
        .test-case {{
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }}
        .test-case-header {{
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 16px;
        }}
        .test-case-content {{
            padding: 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
        }}
        td {{
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }}
        td:first-child {{
            background-color: #f8f9fa;
            font-weight: bold;
            width: 120px;
            color: #2c3e50;
        }}
        tr:last-child td {{
            border-bottom: none;
        }}
        .overview {{
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }}
        .module-stats {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .stat-item {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 5px;
            flex: 1;
            min-width: 200px;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }}
        @media print {{
            body {{ background-color: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>AFTN服务器系统测试用例</h1>

        <div class="overview">
            <h3>测试用例概述</h3>
            <p>本文档包含AFTN服务器系统的完整测试用例，共计 <strong>{len(test_cases)}</strong> 个测试用例，涵盖飞行规划模块、飞行报文模块和动态推演模块三个核心功能模块。</p>

            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-number">100</div>
                    <div>飞行规划模块</div>
                    <div>测试用例 1-100</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100</div>
                    <div>飞行报文模块</div>
                    <div>测试用例 101-200</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100</div>
                    <div>动态推演模块</div>
                    <div>测试用例 201-300</div>
                </div>
            </div>

            <p><strong>预期通过测试用例：</strong> 50个 (约占总数的16.7%)</p>
        </div>
"""
    
    # 按模块分组
    current_module = ""
    for test_case in test_cases:
        if test_case['module'] != current_module:
            current_module = test_case['module']
            html_content += f'\n        <h2>{current_module}测试用例</h2>\n'
        
        html_content += f"""
        <div class="test-case">
            <div class="test-case-header">测试用例 {test_case['id']}</div>
            <div class="test-case-content">
                <table>
                    <tr>
                        <td>ID</td>
                        <td>{test_case['id']}</td>
                    </tr>
                    <tr>
                        <td>模块</td>
                        <td>{test_case['module']}</td>
                    </tr>
                    <tr>
                        <td>用例标题</td>
                        <td>{test_case['title']}</td>
                    </tr>
                    <tr>
                        <td>操作步骤</td>
                        <td>{test_case['steps']}</td>
                    </tr>
                    <tr>
                        <td>预期结果</td>
                        <td>{test_case['expected']}</td>
                    </tr>
                    <tr>
                        <td>备注</td>
                        <td>{test_case['remark']}</td>
                    </tr>
                </table>
            </div>
        </div>"""
    
    html_content += """
    </div>
</body>
</html>"""
    
    return html_content

def main():
    """主函数"""
    print("开始转换Markdown文档到HTML格式...")
    
    # 解析Markdown文件
    test_cases = parse_markdown_file('AFTN_server测试用例.md')
    
    print(f"共解析到 {len(test_cases)} 个测试用例")
    
    # 创建HTML文档
    html_content = create_html_document(test_cases)
    
    # 保存HTML文档
    output_file = 'AFTN服务器系统测试用例.html'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"转换完成！HTML文档已保存为: {output_file}")
    print(f"总计处理了 {len(test_cases)} 个测试用例")
    print("\n使用说明：")
    print("1. 打开生成的HTML文件")
    print("2. 在浏览器中打开后，使用Ctrl+A全选，然后Ctrl+C复制")
    print("3. 在Word中粘贴，即可得到格式化的测试用例文档")

if __name__ == '__main__':
    main()
