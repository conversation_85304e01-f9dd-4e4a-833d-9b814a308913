#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Markdown测试用例文档转换为Word文档
"""

import re
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def create_word_document():
    """创建Word文档并设置基本格式"""
    doc = Document()
    
    # 设置文档标题
    title = doc.add_heading('AFTN服务器系统测试用例', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    return doc

def parse_markdown_file(file_path):
    """解析Markdown文件，提取测试用例"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取测试用例
    test_cases = []
    
    # 使用正则表达式匹配测试用例
    pattern = r'\*\*ID: (\d+)\*\*\n模块：([^\n]+)\n用例标题：([^\n]+)\n操作步骤：([^\n]+)\n预期结果：([^\n]+)\n备注：([^\n]*)'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        test_case = {
            'id': match[0],
            'module': match[1],
            'title': match[2],
            'steps': match[3],
            'expected': match[4],
            'remark': match[5]
        }
        test_cases.append(test_case)
    
    return test_cases

def add_test_case_to_doc(doc, test_case):
    """将测试用例添加到Word文档"""
    # 添加测试用例ID作为小标题
    heading = doc.add_heading(f"测试用例 {test_case['id']}", level=3)
    
    # 创建表格
    table = doc.add_table(rows=6, cols=2)
    table.style = 'Table Grid'
    
    # 设置表格内容
    cells = table.rows[0].cells
    cells[0].text = 'ID'
    cells[1].text = test_case['id']
    
    cells = table.rows[1].cells
    cells[0].text = '模块'
    cells[1].text = test_case['module']
    
    cells = table.rows[2].cells
    cells[0].text = '用例标题'
    cells[1].text = test_case['title']
    
    cells = table.rows[3].cells
    cells[0].text = '操作步骤'
    cells[1].text = test_case['steps']
    
    cells = table.rows[4].cells
    cells[0].text = '预期结果'
    cells[1].text = test_case['expected']
    
    cells = table.rows[5].cells
    cells[0].text = '备注'
    cells[1].text = test_case['remark'] if test_case['remark'] else '（留空）'
    
    # 设置第一列宽度
    for row in table.rows:
        row.cells[0].width = Inches(1.5)
        row.cells[1].width = Inches(4.5)
    
    # 添加空行
    doc.add_paragraph()

def main():
    """主函数"""
    print("开始转换Markdown文档到Word格式...")
    
    # 创建Word文档
    doc = create_word_document()
    
    # 解析Markdown文件
    test_cases = parse_markdown_file('AFTN_server测试用例.md')
    
    print(f"共解析到 {len(test_cases)} 个测试用例")
    
    # 添加概述
    doc.add_heading('测试用例概述', level=1)
    overview = doc.add_paragraph()
    overview.add_run(f'本文档包含AFTN服务器系统的完整测试用例，共计 {len(test_cases)} 个测试用例，')
    overview.add_run('涵盖飞行规划模块、飞行报文模块和动态推演模块三个核心功能模块。')
    
    # 添加模块分类说明
    doc.add_paragraph('• 飞行规划模块：测试用例 1-100')
    doc.add_paragraph('• 飞行报文模块：测试用例 101-200') 
    doc.add_paragraph('• 动态推演模块：测试用例 201-300')
    
    doc.add_paragraph()
    
    # 按模块分组添加测试用例
    current_module = ""
    for test_case in test_cases:
        if test_case['module'] != current_module:
            current_module = test_case['module']
            doc.add_heading(f'{current_module}测试用例', level=1)
        
        add_test_case_to_doc(doc, test_case)
        
        if int(test_case['id']) % 50 == 0:
            print(f"已处理 {test_case['id']} 个测试用例...")
    
    # 保存文档
    output_file = 'AFTN服务器系统测试用例.docx'
    doc.save(output_file)
    
    print(f"转换完成！Word文档已保存为: {output_file}")
    print(f"总计处理了 {len(test_cases)} 个测试用例")

if __name__ == '__main__':
    main()
