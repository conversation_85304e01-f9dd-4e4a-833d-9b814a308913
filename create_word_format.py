#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建Word格式的测试用例文档
"""

import re

def parse_markdown_file(file_path):
    """解析Markdown文件，提取测试用例"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取测试用例
    test_cases = []
    
    # 使用正则表达式匹配测试用例
    pattern = r'\*\*ID: (\d+)\*\*\n模块：([^\n]+)\n用例标题：([^\n]+)\n操作步骤：([^\n]+)\n预期结果：([^\n]+)\n备注：([^\n]*)'
    
    matches = re.findall(pattern, content)
    
    for match in matches:
        test_case = {
            'id': match[0],
            'module': match[1],
            'title': match[2],
            'steps': match[3],
            'expected': match[4],
            'remark': match[5] if match[5] else '（留空）'
        }
        test_cases.append(test_case)
    
    return test_cases

def create_word_format_document(test_cases):
    """创建Word格式的文档内容"""
    content = """AFTN服务器系统测试用例

测试用例概述
本文档包含AFTN服务器系统的完整测试用例，共计 300 个测试用例，涵盖飞行规划模块、飞行报文模块和动态推演模块三个核心功能模块。

模块分布：
• 飞行规划模块：测试用例 1-100
• 飞行报文模块：测试用例 101-200  
• 动态推演模块：测试用例 201-300

预期通过测试用例：50个 (约占总数的16.7%)

================================================================================

"""
    
    # 按模块分组
    current_module = ""
    for test_case in test_cases:
        if test_case['module'] != current_module:
            current_module = test_case['module']
            content += f"\n{current_module}测试用例\n"
            content += "=" * 80 + "\n\n"
        
        content += f"""测试用例 {test_case['id']}
----------------------------------------
ID: {test_case['id']}
模块: {test_case['module']}
用例标题: {test_case['title']}
操作步骤: {test_case['steps']}
预期结果: {test_case['expected']}
备注: {test_case['remark']}

"""
    
    return content

def main():
    """主函数"""
    print("开始创建Word格式的测试用例文档...")
    
    # 解析Markdown文件
    test_cases = parse_markdown_file('AFTN_server测试用例.md')
    
    print(f"共解析到 {len(test_cases)} 个测试用例")
    
    # 创建Word格式文档
    content = create_word_format_document(test_cases)
    
    # 保存文档
    output_file = 'AFTN服务器系统测试用例.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"转换完成！文档已保存为: {output_file}")
    print(f"总计处理了 {len(test_cases)} 个测试用例")
    print("\n使用说明：")
    print("1. 打开生成的txt文件")
    print("2. 全选内容并复制")
    print("3. 在Word中粘贴，然后进行格式调整")
    print("4. 或者直接将txt文件重命名为.doc后缀在Word中打开")

if __name__ == '__main__':
    main()
