# AFTN服务器系统测试用例

## 飞行规划模块测试用例 (1-100)

### TrackController 测试用例 (1-50)

**ID: 1**
模块：飞行规划模块
用例标题：验证正常创建飞行轨迹
操作步骤：1. 调用/track/insertTrack接口 2. 输入完整的轨迹信息(trackName="测试轨迹001", trackCode="TC001", aircraftId="AC001", planDepartureTime="2025-01-01 08:00:00", planArrivalTime="2025-01-01 12:00:00", maxFlightHigh=10000, maxFlightSpeed=800)
预期结果：系统应成功创建轨迹，返回成功状态和轨迹ID
备注：

**ID: 2**
模块：飞行规划模块
用例标题：验证轨迹名称为空时的异常处理
操作步骤：1. 调用/track/insertTrack接口 2. 输入轨迹信息但trackName为空
预期结果：系统应返回错误信息"轨迹名称不能为空!"
备注：

**ID: 3**
模块：飞行规划模块
用例标题：验证轨迹编号为空时的异常处理
操作步骤：1. 调用/track/insertTrack接口 2. 输入轨迹信息但trackCode为空
预期结果：系统应返回错误信息"飞行轨迹编号不能为空!"
备注：

**ID: 4**
模块：飞行规划模块
用例标题：验证飞机ID为空时的异常处理
操作步骤：1. 调用/track/insertTrack接口 2. 输入轨迹信息但aircraftId为空
预期结果：系统应返回错误信息"飞机id不能为空!"
备注：

**ID: 5**
模块：飞行规划模块
用例标题：验证重复轨迹名称的异常处理
操作步骤：1. 先创建一个轨迹 2. 再次调用/track/insertTrack接口使用相同的trackName
预期结果：系统应返回错误信息"轨迹名称不能重复添加!"
备注：

**ID: 6**
模块：飞行规划模块
用例标题：验证重复轨迹编号的异常处理
操作步骤：1. 先创建一个轨迹 2. 再次调用/track/insertTrack接口使用相同的trackCode
预期结果：系统应返回错误信息"飞行轨迹编号不能重复添加!"
备注：

**ID: 7**
模块：飞行规划模块
用例标题：验证分页查询轨迹数据
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入分页参数(page=1, size=10)
预期结果：系统应返回分页的轨迹数据列表
备注：

**ID: 8**
模块：飞行规划模块
用例标题：验证按轨迹名称模糊查询
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入trackName="测试"进行模糊查询
预期结果：系统应返回包含"测试"关键字的轨迹列表
备注：

**ID: 9**
模块：飞行规划模块
用例标题：验证按轨迹编号模糊查询
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入trackCode="TC"进行模糊查询
预期结果：系统应返回包含"TC"关键字的轨迹列表
备注：

**ID: 10**
模块：飞行规划模块
用例标题：验证按状态查询轨迹
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入status="create"查询未开始的轨迹
预期结果：系统应返回状态为create的轨迹列表
备注：

**ID: 11**
模块：飞行规划模块
用例标题：验证按飞机型号查询轨迹
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入aircraftId="AC001"
预期结果：系统应返回使用指定飞机的轨迹列表
备注：

**ID: 12**
模块：飞行规划模块
用例标题：验证按创建时间排序查询
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入sortField="createTime", sortSequential="DESC"
预期结果：系统应返回按创建时间降序排列的轨迹列表
备注：

**ID: 13**
模块：飞行规划模块
用例标题：验证按轨迹长度排序查询
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入sortField="trackLength", sortSequential="ASC"
预期结果：系统应返回按轨迹长度升序排列的轨迹列表
备注：

**ID: 14**
模块：飞行规划模块
用例标题：验证查询轨迹详情
操作步骤：1. 调用/track/getTrackInfo接口 2. 输入有效的trackId
预期结果：系统应返回完整的轨迹详情信息，包括飞机信息
备注：

**ID: 15**
模块：飞行规划模块
用例标题：验证查询不存在轨迹的异常处理
操作步骤：1. 调用/track/getTrackInfo接口 2. 输入不存在的trackId
预期结果：系统应返回空或错误信息
备注：

**ID: 16**
模块：飞行规划模块
用例标题：验证查询轨迹绑定信息
操作步骤：1. 调用/track/getTrackBindInfo接口 2. 输入有效的trackId
预期结果：系统应返回轨迹绑定的区域列表和航线列表
备注：

**ID: 17**
模块：飞行规划模块
用例标题：验证正常修改轨迹信息
操作步骤：1. 调用/track/updateTrack接口 2. 输入trackId和要修改的字段信息
预期结果：系统应成功修改轨迹信息并返回成功状态
备注：

**ID: 18**
模块：飞行规划模块
用例标题：验证修改轨迹名称为已存在名称
操作步骤：1. 调用/track/updateTrack接口 2. 修改trackName为已存在的名称
预期结果：系统应返回错误信息"轨迹名称不能重复!"
备注：

**ID: 19**
模块：飞行规划模块
用例标题：验证修改轨迹编号为已存在编号
操作步骤：1. 调用/track/updateTrack接口 2. 修改trackCode为已存在的编号
预期结果：系统应返回错误信息"飞行轨迹编号不能重复添加!"
备注：

**ID: 20**
模块：飞行规划模块
用例标题：验证修改飞机型号
操作步骤：1. 调用/track/updateTrack接口 2. 修改aircraftId为新的飞机ID
预期结果：系统应成功修改飞机型号
备注：

**ID: 21**
模块：飞行规划模块
用例标题：验证修改计划起飞时间
操作步骤：1. 调用/track/updateTrack接口 2. 修改planDepartureTime为新时间
预期结果：系统应成功修改计划起飞时间
备注：

**ID: 22**
模块：飞行规划模块
用例标题：验证修改计划降落时间
操作步骤：1. 调用/track/updateTrack接口 2. 修改planArrivalTime为新时间
预期结果：系统应成功修改计划降落时间
备注：

**ID: 23**
模块：飞行规划模块
用例标题：验证修改巡航高度
操作步骤：1. 调用/track/updateTrack接口 2. 修改maxFlightHigh为新高度值
预期结果：系统应成功修改巡航高度
备注：

**ID: 24**
模块：飞行规划模块
用例标题：验证修改巡航速度
操作步骤：1. 调用/track/updateTrack接口 2. 修改maxFlightSpeed为新速度值
预期结果：系统应成功修改巡航速度
备注：

**ID: 25**
模块：飞行规划模块
用例标题：验证修改最大爬升率
操作步骤：1. 调用/track/updateTrack接口 2. 修改maxClimbRate为新值
预期结果：系统应成功修改最大爬升率
备注：

**ID: 26**
模块：飞行规划模块
用例标题：验证修改最小转弯半径
操作步骤：1. 调用/track/updateTrack接口 2. 修改minTurningCircle为新值
预期结果：系统应成功修改最小转弯半径
备注：

**ID: 27**
模块：飞行规划模块
用例标题：验证正常删除轨迹
操作步骤：1. 调用/track/deleteTrack接口 2. 输入有效的trackId（状态为create或closed）
预期结果：系统应成功删除轨迹并返回成功状态
备注：

**ID: 28**
模块：飞行规划模块
用例标题：验证删除正在推演中的轨迹
操作步骤：1. 调用/track/deleteTrack接口 2. 输入状态为running的trackId
预期结果：系统应返回错误信息"轨迹正在推演中，不能删除!"
备注：

**ID: 29**
模块：飞行规划模块
用例标题：验证删除不存在的轨迹
操作步骤：1. 调用/track/deleteTrack接口 2. 输入不存在的trackId
预期结果：系统应返回错误信息或处理异常
备注：

**ID: 30**
模块：飞行规划模块
用例标题：验证trackId参数为空的异常处理
操作步骤：1. 调用/track/getTrackInfo接口 2. 不传入trackId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 31**
模块：飞行规划模块
用例标题：验证创建轨迹时巡航高度为负数
操作步骤：1. 调用/track/insertTrack接口 2. 输入maxFlightHigh=-1000
预期结果：系统应返回参数验证错误或成功创建（取决于业务规则）
备注：

**ID: 32**
模块：飞行规划模块
用例标题：验证创建轨迹时巡航速度为负数
操作步骤：1. 调用/track/insertTrack接口 2. 输入maxFlightSpeed=-500
预期结果：系统应返回参数验证错误或成功创建（取决于业务规则）
备注：

**ID: 33**
模块：飞行规划模块
用例标题：验证创建轨迹时起飞时间晚于降落时间
操作步骤：1. 调用/track/insertTrack接口 2. 输入planDepartureTime晚于planArrivalTime
预期结果：系统应返回时间逻辑错误或成功创建（取决于业务规则）
备注：

**ID: 34**
模块：飞行规划模块
用例标题：验证查询轨迹时页码为0
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入page=0
预期结果：系统应返回第一页数据或参数错误
备注：

**ID: 35**
模块：飞行规划模块
用例标题：验证查询轨迹时页长为0
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入size=0
预期结果：系统应返回空列表或参数错误
备注：

**ID: 36**
模块：飞行规划模块
用例标题：验证查询轨迹时页码为负数
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入page=-1
预期结果：系统应返回参数错误或默认处理
备注：

**ID: 37**
模块：飞行规划模块
用例标题：验证查询轨迹时页长为负数
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入size=-10
预期结果：系统应返回参数错误或默认处理
备注：

**ID: 38**
模块：飞行规划模块
用例标题：验证查询轨迹时使用无效状态值
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入status="invalid"
预期结果：系统应返回参数错误或忽略该条件
备注：

**ID: 39**
模块：飞行规划模块
用例标题：验证查询轨迹时使用无效排序字段
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入sortField="invalidField"
预期结果：系统应忽略排序或返回错误
备注：

**ID: 40**
模块：飞行规划模块
用例标题：验证查询轨迹时使用无效排序顺序
操作步骤：1. 调用/track/queryTrackByPage接口 2. 输入sortSequential="INVALID"
预期结果：系统应使用默认排序或返回错误
备注：

**ID: 41**
模块：飞行规划模块
用例标题：验证修改不存在轨迹的异常处理
操作步骤：1. 调用/track/updateTrack接口 2. 输入不存在的trackId
预期结果：系统应返回轨迹不存在的错误信息
备注：

**ID: 42**
模块：飞行规划模块
用例标题：验证创建轨迹时使用超长轨迹名称
操作步骤：1. 调用/track/insertTrack接口 2. 输入超过字段长度限制的trackName
预期结果：系统应返回字段长度超限错误
备注：

**ID: 43**
模块：飞行规划模块
用例标题：验证创建轨迹时使用超长轨迹编号
操作步骤：1. 调用/track/insertTrack接口 2. 输入超过字段长度限制的trackCode
预期结果：系统应返回字段长度超限错误
备注：

**ID: 44**
模块：飞行规划模块
用例标题：验证创建轨迹时使用特殊字符
操作步骤：1. 调用/track/insertTrack接口 2. 在trackName中输入特殊字符如<script>
预期结果：系统应正确处理特殊字符或进行转义
备注：

**ID: 45**
模块：飞行规划模块
用例标题：验证创建轨迹时使用不存在的飞机ID
操作步骤：1. 调用/track/insertTrack接口 2. 输入不存在的aircraftId
预期结果：系统应返回飞机不存在的错误信息
备注：

**ID: 46**
模块：飞行规划模块
用例标题：验证查询轨迹绑定信息时trackId为空
操作步骤：1. 调用/track/getTrackBindInfo接口 2. 不传入trackId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 47**
模块：飞行规划模块
用例标题：验证大数据量分页查询性能
操作步骤：1. 创建大量轨迹数据 2. 调用/track/queryTrackByPage接口查询后面的页码
预期结果：系统应在合理时间内返回查询结果
备注：

**ID: 48**
模块：飞行规划模块
用例标题：验证并发创建轨迹
操作步骤：1. 同时发起多个/track/insertTrack请求 2. 使用不同的轨迹信息
预期结果：系统应正确处理并发请求，所有轨迹都能成功创建
备注：

**ID: 49**
模块：飞行规划模块
用例标题：验证并发修改同一轨迹
操作步骤：1. 同时发起多个/track/updateTrack请求 2. 修改同一个轨迹的不同字段
预期结果：系统应正确处理并发修改，保证数据一致性
备注：

**ID: 50**
模块：飞行规划模块
用例标题：验证创建轨迹后状态初始化
操作步骤：1. 调用/track/insertTrack接口创建轨迹 2. 查询创建的轨迹详情
预期结果：系统应将轨迹状态初始化为create，轨迹长度为0.0
备注：

### TrackLineStringController 测试用例 (51-100)

**ID: 51**
模块：飞行规划模块
用例标题：验证查询航迹线列表
操作步骤：1. 调用/trackLineString/trackLineStringList接口 2. 输入有效的trackId
预期结果：系统应返回该轨迹的所有航迹线列表，包含航迹点和机场信息
备注：

**ID: 52**
模块：飞行规划模块
用例标题：验证查询不存在轨迹的航迹线
操作步骤：1. 调用/trackLineString/trackLineStringList接口 2. 输入不存在的trackId
预期结果：系统应返回空列表或错误信息
备注：

**ID: 53**
模块：飞行规划模块
用例标题：验证trackId为空时的异常处理
操作步骤：1. 调用/trackLineString/trackLineStringList接口 2. 不传入trackId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 54**
模块：飞行规划模块
用例标题：验证查询规划报文列表
操作步骤：1. 调用/trackLineString/selectPlanMessageList接口 2. 输入trackId和messageType="FPL"
预期结果：系统应返回可用的FPL报文列表
备注：

**ID: 55**
模块：飞行规划模块
用例标题：验证查询DEP_ARR报文可用航线
操作步骤：1. 调用/trackLineString/selectDepArrPlanLineStringList接口 2. 输入trackId、depMessageId、arrMessageId
预期结果：系统应返回起降机场匹配的航线列表
备注：

**ID: 56**
模块：飞行规划模块
用例标题：验证DEP_ARR报文ID为空的异常处理
操作步骤：1. 调用/trackLineString/selectDepArrPlanLineStringList接口 2. depMessageId或arrMessageId为空
预期结果：系统应返回参数错误信息
备注：

**ID: 57**
模块：飞行规划模块
用例标题：验证新增DEP_ARR航迹线
操作步骤：1. 调用/trackLineString/insertDepArrTrackLineString接口 2. 输入完整参数
预期结果：系统应成功创建航迹线并返回航迹线信息
备注：

**ID: 58**
模块：飞行规划模块
用例标题：验证重复绑定航迹线的异常处理
操作步骤：1. 先绑定一条航线 2. 再次绑定相同的航线到同一轨迹
预期结果：系统应返回错误信息"航迹点不能重复绑定"
备注：

**ID: 59**
模块：飞行规划模块
用例标题：验证查询领航报可用航线列表
操作步骤：1. 调用/trackLineString/selectPlanLineStringList接口 2. 输入trackId和messageId
预期结果：系统应返回起降地点匹配的航线列表
备注：

**ID: 60**
模块：飞行规划模块
用例标题：验证航迹线详情查询
操作步骤：1. 调用/trackLineString/trackLineStringInfo接口 2. 输入trackId和trackLineStringId
预期结果：系统应返回航迹线详细信息，包括机场和飞机信息
备注：

**ID: 61**
模块：飞行规划模块
用例标题：验证新增FPL航迹线
操作步骤：1. 调用/trackLineString/insertTrackLineString接口 2. 输入trackId、lineStringId、messageId等参数
预期结果：系统应成功创建航迹线并返回航迹线信息
备注：

**ID: 62**
模块：飞行规划模块
用例标题：验证删除航迹线
操作步骤：1. 调用/trackLineString/deleteTrackLineString接口 2. 输入trackId和trackLineStringId
预期结果：系统应成功删除航迹线及相关的航迹点和报文绑定
备注：

**ID: 63**
模块：飞行规划模块
用例标题：验证删除不存在的航迹线
操作步骤：1. 调用/trackLineString/deleteTrackLineString接口 2. 输入不存在的trackLineStringId
预期结果：系统应正常处理或返回相应错误信息
备注：

**ID: 64**
模块：飞行规划模块
用例标题：验证航迹线颜色设置
操作步骤：1. 调用新增航迹线接口 2. 设置lineStringColor参数为有效颜色值
预期结果：系统应成功设置航迹线颜色
备注：

**ID: 65**
模块：飞行规划模块
用例标题：验证航迹线颜色为无效值
操作步骤：1. 调用新增航迹线接口 2. 设置lineStringColor为无效颜色值
预期结果：系统应使用默认颜色或返回参数错误
备注：

**ID: 66**
模块：飞行规划模块
用例标题：验证查询航迹线时包含航迹点信息
操作步骤：1. 创建包含航迹点的航迹线 2. 调用查询航迹线列表接口
预期结果：系统应返回航迹线及其包含的所有航迹点信息
备注：

**ID: 67**
模块：飞行规划模块
用例标题：验证航迹线按序号排序
操作步骤：1. 创建多条航迹线并设置不同序号 2. 查询航迹线列表
预期结果：系统应按sequence字段升序返回航迹线列表
备注：

**ID: 68**
模块：飞行规划模块
用例标题：验证航迹线关联机场信息
操作步骤：1. 创建航迹线 2. 查询航迹线详情
预期结果：系统应返回航迹线关联的起飞和降落机场详细信息
备注：

**ID: 69**
模块：飞行规划模块
用例标题：验证航迹线关联飞机信息
操作步骤：1. 创建航迹线 2. 查询航迹线详情
预期结果：系统应返回航迹线关联的飞机详细信息
备注：

**ID: 70**
模块：飞行规划模块
用例标题：验证使用不存在报文创建航迹线
操作步骤：1. 调用新增航迹线接口 2. 输入不存在的messageId
预期结果：系统应返回报文不存在的错误信息
备注：

**ID: 71**
模块：飞行规划模块
用例标题：验证使用不存在航线创建航迹线
操作步骤：1. 调用新增航迹线接口 2. 输入不存在的lineStringId
预期结果：系统应返回航线不存在的错误信息
备注：

**ID: 72**
模块：飞行规划模块
用例标题：验证报文参数解析功能
操作步骤：1. 使用包含StartPlace和EndPlace参数的报文 2. 查询可用航线列表
预期结果：系统应正确解析报文参数并返回匹配的航线
备注：

**ID: 73**
模块：飞行规划模块
用例标题：验证报文参数为空时的处理
操作步骤：1. 使用StartPlace或EndPlace为空的报文 2. 查询可用航线列表
预期结果：系统应返回空列表或所有航线
备注：

**ID: 74**
模块：飞行规划模块
用例标题：验证航迹线创建后轨迹长度更新
操作步骤：1. 创建航迹线 2. 查询轨迹信息
预期结果：系统应更新轨迹的总长度信息
备注：

**ID: 75**
模块：飞行规划模块
用例标题：验证删除航迹线后轨迹长度更新
操作步骤：1. 删除航迹线 2. 查询轨迹信息
预期结果：系统应重新计算并更新轨迹的总长度
备注：

**ID: 76**
模块：飞行规划模块
用例标题：验证航迹线与报文的绑定关系
操作步骤：1. 创建航迹线时绑定报文 2. 查询航迹线信息
预期结果：系统应正确建立航迹线与报文的绑定关系
备注：

**ID: 77**
模块：飞行规划模块
用例标题：验证删除航迹线时清理绑定报文
操作步骤：1. 删除航迹线 2. 查询相关的报文绑定信息
预期结果：系统应清理航迹线相关的所有报文绑定关系
备注：

**ID: 78**
模块：飞行规划模块
用例标题：验证航迹线序号自动分配
操作步骤：1. 连续创建多条航迹线 2. 查询航迹线列表
预期结果：系统应自动为航迹线分配递增的序号
备注：

**ID: 79**
模块：飞行规划模块
用例标题：验证航迹线起降时间设置
操作步骤：1. 创建航迹线时设置起降时间 2. 查询航迹线详情
预期结果：系统应正确保存和返回航迹线的起降时间
备注：

**ID: 80**
模块：飞行规划模块
用例标题：验证航迹线起降时间逻辑校验
操作步骤：1. 创建航迹线时设置起飞时间晚于降落时间 2. 查看系统处理结果
预期结果：系统应进行时间逻辑校验或正常保存
备注：

**ID: 81**
模块：飞行规划模块
用例标题：验证多条航迹线的时间顺序
操作步骤：1. 为同一轨迹创建多条航迹线 2. 设置不同的起降时间
预期结果：系统应按时间顺序正确排列航迹线
备注：

**ID: 82**
模块：飞行规划模块
用例标题：验证航迹线包含的航迹点数量
操作步骤：1. 创建包含多个航迹点的航迹线 2. 查询航迹线详情
预期结果：系统应返回正确的航迹点数量和详细信息
备注：

**ID: 83**
模块：飞行规划模块
用例标题：验证航迹点的经纬度信息
操作步骤：1. 查询航迹线的航迹点信息 2. 检查经纬度数据
预期结果：系统应返回正确格式的经纬度坐标信息
备注：

**ID: 84**
模块：飞行规划模块
用例标题：验证航迹点的高度和速度信息
操作步骤：1. 查询航迹线的航迹点信息 2. 检查高度和速度数据
预期结果：系统应返回正确的高度和速度信息
备注：

**ID: 85**
模块：飞行规划模块
用例标题：验证航迹线的距离计算
操作步骤：1. 创建包含多个航迹点的航迹线 2. 查询航迹线长度信息
预期结果：系统应正确计算航迹线的总距离
备注：

**ID: 86**
模块：飞行规划模块
用例标题：验证航迹线的飞行时间计算
操作步骤：1. 创建航迹线 2. 查询预计飞行时间
预期结果：系统应根据距离和速度正确计算飞行时间
备注：

**ID: 87**
模块：飞行规划模块
用例标题：验证航迹线的燃油消耗计算
操作步骤：1. 创建航迹线 2. 查询预计燃油消耗
预期结果：系统应根据飞机型号和飞行距离计算燃油消耗
备注：

**ID: 88**
模块：飞行规划模块
用例标题：验证并发创建航迹线
操作步骤：1. 同时为同一轨迹创建多条航迹线 2. 检查创建结果
预期结果：系统应正确处理并发请求，所有航迹线都能成功创建
备注：

**ID: 89**
模块：飞行规划模块
用例标题：验证并发删除航迹线
操作步骤：1. 同时删除同一轨迹的多条航迹线 2. 检查删除结果
预期结果：系统应正确处理并发删除，保证数据一致性
备注：

**ID: 90**
模块：飞行规划模块
用例标题：验证航迹线数据完整性
操作步骤：1. 创建完整的航迹线 2. 查询并验证所有相关数据
预期结果：系统应保证航迹线、航迹点、报文绑定等数据的完整性
备注：

**ID: 91**
模块：飞行规划模块
用例标题：验证航迹线状态管理
操作步骤：1. 创建航迹线 2. 查询航迹线状态信息
预期结果：系统应正确管理航迹线的状态信息
备注：

**ID: 92**
模块：飞行规划模块
用例标题：验证航迹线权限控制
操作步骤：1. 使用不同权限用户操作航迹线 2. 检查操作结果
预期结果：系统应根据用户权限控制航迹线操作
备注：

**ID: 93**
模块：飞行规划模块
用例标题：验证航迹线历史记录
操作步骤：1. 修改航迹线信息 2. 查询历史记录
预期结果：系统应记录航迹线的修改历史
备注：

**ID: 94**
模块：飞行规划模块
用例标题：验证航迹线导出功能
操作步骤：1. 选择航迹线 2. 执行导出操作
预期结果：系统应能够导出航迹线数据到指定格式
备注：

**ID: 95**
模块：飞行规划模块
用例标题：验证航迹线导入功能
操作步骤：1. 准备航迹线数据文件 2. 执行导入操作
预期结果：系统应能够从文件导入航迹线数据
备注：

**ID: 96**
模块：飞行规划模块
用例标题：验证航迹线备份恢复
操作步骤：1. 备份航迹线数据 2. 删除后恢复数据
预期结果：系统应能够正确备份和恢复航迹线数据
备注：

**ID: 97**
模块：飞行规划模块
用例标题：验证航迹线性能优化
操作步骤：1. 创建大量航迹线 2. 测试查询性能
预期结果：系统应在合理时间内完成大数据量的航迹线查询
备注：

**ID: 98**
模块：飞行规划模块
用例标题：验证航迹线缓存机制
操作步骤：1. 多次查询同一航迹线 2. 检查响应时间
预期结果：系统应通过缓存提高重复查询的响应速度
备注：

**ID: 99**
模块：飞行规划模块
用例标题：验证航迹线事务处理
操作步骤：1. 在事务中创建航迹线和相关数据 2. 模拟异常情况
预期结果：系统应保证事务的原子性，异常时回滚所有操作
备注：

**ID: 100**
模块：飞行规划模块
用例标题：验证航迹线日志记录
操作步骤：1. 执行航迹线相关操作 2. 查看系统日志
预期结果：系统应记录详细的操作日志用于审计和问题排查
备注：

## 飞行报文模块测试用例 (101-200)

### MessageController 测试用例 (101-170)

**ID: 101**
模块：飞行报文模块
用例标题：验证查询报文列表（无分页）
操作步骤：1. 调用/message/selectMessageList接口 2. 输入messageType="FPL"
预期结果：系统应返回指定类型的所有报文列表
备注：

**ID: 102**
模块：飞行报文模块
用例标题：验证查询所有类型报文
操作步骤：1. 调用/message/selectMessageList接口 2. 不传入messageType参数
预期结果：系统应返回所有类型的报文列表
备注：

**ID: 103**
模块：飞行报文模块
用例标题：验证分页查询报文
操作步骤：1. 调用/message/selectMessagePage接口 2. 输入page=1, size=10
预期结果：系统应返回分页的报文数据，排除私有用途报文
备注：

**ID: 104**
模块：飞行报文模块
用例标题：验证按报文类型分页查询
操作步骤：1. 调用/message/selectMessagePage接口 2. 输入messageType="DEP"
预期结果：系统应返回指定类型的分页报文数据
备注：

**ID: 105**
模块：飞行报文模块
用例标题：验证按报文编号模糊查询
操作步骤：1. 调用/message/selectMessagePage接口 2. 输入messageCode="MSG"
预期结果：系统应返回包含"MSG"关键字的报文列表
备注：

**ID: 106**
模块：飞行报文模块
用例标题：验证按报文名称模糊查询
操作步骤：1. 调用/message/selectMessagePage接口 2. 输入messageName="测试"
预期结果：系统应返回包含"测试"关键字的报文列表
备注：

**ID: 107**
模块：飞行报文模块
用例标题：验证按报文用途查询
操作步骤：1. 调用/message/selectMessagePage接口 2. 输入messagePurpose="track"
预期结果：系统应返回用途为track的报文列表
备注：

**ID: 108**
模块：飞行报文模块
用例标题：验证根据报文类型查询参数树
操作步骤：1. 调用/message/selectParamsTreeByMessageType接口 2. 输入messageType="FPL", aircraftId="AC001"
预期结果：系统应返回报文参数树结构和新生成的messageId
备注：

**ID: 109**
模块：飞行报文模块
用例标题：验证报文类型为空的异常处理
操作步骤：1. 调用/message/selectParamsTreeByMessageType接口 2. 不传入messageType参数
预期结果：系统应返回参数错误信息
备注：

**ID: 110**
模块：飞行报文模块
用例标题：验证飞机ID为空的异常处理
操作步骤：1. 调用/message/selectParamsTreeByMessageType接口 2. 不传入aircraftId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 111**
模块：飞行报文模块
用例标题：验证查询报文详情
操作步骤：1. 调用/message/selectMessageDetail接口 2. 输入有效的messageId
预期结果：系统应返回报文详情和参数树结构
备注：

**ID: 112**
模块：飞行报文模块
用例标题：验证查询不存在报文的异常处理
操作步骤：1. 调用/message/selectMessageDetail接口 2. 输入不存在的messageId
预期结果：系统应返回报文不存在的错误信息
备注：

**ID: 113**
模块：飞行报文模块
用例标题：验证正常新增报文
操作步骤：1. 调用/message/insertMessage接口 2. 输入完整的报文信息
预期结果：系统应成功创建报文并返回报文信息
备注：

**ID: 114**
模块：飞行报文模块
用例标题：验证报文ID为空的异常处理
操作步骤：1. 调用/message/insertMessage接口 2. 不传入messageId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 115**
模块：飞行报文模块
用例标题：验证报文编号为空的异常处理
操作步骤：1. 调用/message/insertMessage接口 2. 不传入messageCode参数
预期结果：系统应返回参数错误信息
备注：

**ID: 116**
模块：飞行报文模块
用例标题：验证报文名称为空的异常处理
操作步骤：1. 调用/message/insertMessage接口 2. 不传入messageName参数
预期结果：系统应返回参数错误信息
备注：

**ID: 117**
模块：飞行报文模块
用例标题：验证飞机ID为空的异常处理
操作步骤：1. 调用/message/insertMessage接口 2. 不传入aircraftId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 118**
模块：飞行报文模块
用例标题：验证报文类型为空的异常处理
操作步骤：1. 调用/message/insertMessage接口 2. 不传入messageType参数
预期结果：系统应返回参数错误信息
备注：

**ID: 119**
模块：飞行报文模块
用例标题：验证报文用途默认值设置
操作步骤：1. 调用/message/insertMessage接口 2. 不传入messagePurpose参数
预期结果：系统应使用默认值track作为报文用途
备注：

**ID: 120**
模块：飞行报文模块
用例标题：验证报文状态初始化
操作步骤：1. 创建报文 2. 查询报文详情
预期结果：系统应将报文状态初始化为created
备注：

**ID: 121**
模块：飞行报文模块
用例标题：验证修改报文信息
操作步骤：1. 调用/message/updateMessage接口 2. 输入messageId和要修改的字段
预期结果：系统应成功修改报文信息
备注：

**ID: 122**
模块：飞行报文模块
用例标题：验证修改不存在报文的异常处理
操作步骤：1. 调用/message/updateMessage接口 2. 输入不存在的messageId
预期结果：系统应返回报文不存在的错误信息
备注：

**ID: 123**
模块：飞行报文模块
用例标题：验证修改报文参数
操作步骤：1. 调用/message/updateMessageParam接口 2. 输入paramId和新的paramValue
预期结果：系统应成功修改报文参数值
备注：

**ID: 124**
模块：飞行报文模块
用例标题：验证修改日期类型参数
操作步骤：1. 调用/message/updateMessageParam接口 2. 修改ParamType为Date的参数
预期结果：系统应正确转换日期格式并保存
备注：

**ID: 125**
模块：飞行报文模块
用例标题：验证修改缓存中的参数
操作步骤：1. 对未保存到数据库的报文参数进行修改 2. 检查缓存更新
预期结果：系统应正确更新缓存中的参数值
备注：

**ID: 126**
模块：飞行报文模块
用例标题：验证报文解释功能
操作步骤：1. 调用/message/messageExplain接口 2. 输入messageId和messageType
预期结果：系统应返回报文的解释结果和内容
备注：

**ID: 127**
模块：飞行报文模块
用例标题：验证解释不存在报文的处理
操作步骤：1. 调用/message/messageExplain接口 2. 输入不存在的messageId
预期结果：系统应从缓存中获取参数或返回相应错误
备注：

**ID: 128**
模块：飞行报文模块
用例标题：验证报文解释异常处理
操作步骤：1. 调用/message/messageExplain接口 2. 使用会导致解析异常的报文
预期结果：系统应返回"获取报文失败"的友好错误信息
备注：

**ID: 129**
模块：飞行报文模块
用例标题：验证报文解释结果格式
操作步骤：1. 成功解释报文 2. 检查返回结果格式
预期结果：系统应返回包含result和messageContent的JSON格式数据
备注：

**ID: 130**
模块：飞行报文模块
用例标题：验证报文记录更新
操作步骤：1. 解释报文 2. 查询报文发送记录
预期结果：系统应更新MessageRecord表中的报文内容
备注：

**ID: 131**
模块：飞行报文模块
用例标题：验证创建报文时参数自动填充
操作步骤：1. 使用包含飞机信息的aircraftId创建报文 2. 查看参数树
预期结果：系统应自动填充飞机相关的参数信息
备注：

**ID: 132**
模块：飞行报文模块
用例标题：验证报文参数树构建
操作步骤：1. 查询包含对象和枚举类型参数的报文 2. 检查参数树结构
预期结果：系统应正确构建包含元数据的参数树结构
备注：

**ID: 133**
模块：飞行报文模块
用例标题：验证报文编号重复性检查
操作步骤：1. 创建报文 2. 使用相同messageCode创建另一个报文
预期结果：系统应允许或拒绝重复的报文编号（取决于业务规则）
备注：

**ID: 134**
模块：飞行报文模块
用例标题：验证报文名称重复性检查
操作步骤：1. 创建报文 2. 使用相同messageName创建另一个报文
预期结果：系统应允许或拒绝重复的报文名称（取决于业务规则）
备注：

**ID: 135**
模块：飞行报文模块
用例标题：验证报文内容长度限制
操作步骤：1. 调用/message/insertMessage接口 2. 输入超长的messageContent
预期结果：系统应返回字段长度超限错误或截断处理
备注：

**ID: 136**
模块：飞行报文模块
用例标题：验证报文结果长度限制
操作步骤：1. 调用/message/insertMessage接口 2. 输入超长的messageResult
预期结果：系统应返回字段长度超限错误或截断处理
备注：

**ID: 137**
模块：飞行报文模块
用例标题：验证报文结论长度限制
操作步骤：1. 调用/message/insertMessage接口 2. 输入超长的messageConclusion
预期结果：系统应返回字段长度超限错误或截断处理
备注：

**ID: 138**
模块：飞行报文模块
用例标题：验证接收对象长度限制
操作步骤：1. 调用/message/insertMessage接口 2. 输入超长的receiver
预期结果：系统应返回字段长度超限错误或截断处理
备注：

**ID: 139**
模块：飞行报文模块
用例标题：验证报文特殊字符处理
操作步骤：1. 在报文内容中输入特殊字符 2. 创建并查询报文
预期结果：系统应正确处理和存储特殊字符
备注：

**ID: 140**
模块：飞行报文模块
用例标题：验证报文HTML标签过滤
操作步骤：1. 在报文内容中输入HTML标签 2. 创建并查询报文
预期结果：系统应过滤或转义HTML标签以防止XSS攻击
备注：

**ID: 141**
模块：飞行报文模块
用例标题：验证报文SQL注入防护
操作步骤：1. 在报文参数中输入SQL注入代码 2. 创建并查询报文
预期结果：系统应防止SQL注入攻击，正常处理输入
备注：

**ID: 142**
模块：飞行报文模块
用例标题：验证报文创建时间自动设置
操作步骤：1. 创建报文 2. 查询报文详情检查createTime
预期结果：系统应自动设置报文的创建时间为当前时间
备注：

**ID: 143**
模块：飞行报文模块
用例标题：验证报文ID唯一性
操作步骤：1. 多次调用参数树生成接口 2. 检查生成的messageId
预期结果：系统应为每次调用生成唯一的messageId
备注：

**ID: 144**
模块：飞行报文模块
用例标题：验证报文参数缓存机制
操作步骤：1. 生成报文参数树 2. 多次查询相同报文的参数
预期结果：系统应通过缓存提高参数查询性能
备注：

**ID: 145**
模块：飞行报文模块
用例标题：验证报文参数缓存过期
操作步骤：1. 生成报文参数并等待缓存过期 2. 再次查询参数
预期结果：系统应正确处理缓存过期，重新加载参数
备注：

**ID: 146**
模块：飞行报文模块
用例标题：验证报文用途枚举值校验
操作步骤：1. 调用/message/insertMessage接口 2. 输入无效的messagePurpose值
预期结果：系统应返回枚举值无效的错误信息
备注：

**ID: 147**
模块：飞行报文模块
用例标题：验证私有报文过滤
操作步骤：1. 创建用途为privately的报文 2. 调用分页查询接口
预期结果：系统应在分页查询中过滤掉私有用途的报文
备注：

**ID: 148**
模块：飞行报文模块
用例标题：验证报文按创建时间排序
操作步骤：1. 创建多个报文 2. 调用分页查询接口
预期结果：系统应按创建时间降序返回报文列表
备注：

**ID: 149**
模块：飞行报文模块
用例标题：验证报文类型名称转换
操作步骤：1. 查询报文分页数据 2. 检查返回的messageType字段
预期结果：系统应将报文类型代码转换为可读的名称
备注：

**ID: 150**
模块：飞行报文模块
用例标题：验证报文参数类型处理
操作步骤：1. 创建包含不同参数类型的报文 2. 查询参数详情
预期结果：系统应正确处理Object、Enum、String等不同参数类型
备注：

**ID: 151**
模块：飞行报文模块
用例标题：验证报文参数值为空的处理
操作步骤：1. 修改报文参数值为空字符串 2. 查看处理结果
预期结果：系统应正确处理空参数值
备注：

**ID: 152**
模块：飞行报文模块
用例标题：验证报文参数值为null的处理
操作步骤：1. 修改报文参数值为null 2. 查看处理结果
预期结果：系统应正确处理null参数值
备注：

**ID: 153**
模块：飞行报文模块
用例标题：验证报文参数数值类型校验
操作步骤：1. 为数值类型参数输入非数值字符串 2. 保存参数
预期结果：系统应进行类型校验或类型转换
备注：

**ID: 154**
模块：飞行报文模块
用例标题：验证报文参数日期格式校验
操作步骤：1. 为日期类型参数输入无效日期格式 2. 保存参数
预期结果：系统应进行日期格式校验并返回错误信息
备注：

**ID: 155**
模块：飞行报文模块
用例标题：验证报文参数枚举值校验
操作步骤：1. 为枚举类型参数输入无效枚举值 2. 保存参数
预期结果：系统应进行枚举值校验并返回错误信息
备注：

**ID: 156**
模块：飞行报文模块
用例标题：验证报文解释超时处理
操作步骤：1. 调用报文解释接口 2. 模拟C端服务超时
预期结果：系统应在超时后返回友好的错误信息
备注：

**ID: 157**
模块：飞行报文模块
用例标题：验证报文解释网络异常处理
操作步骤：1. 调用报文解释接口 2. 模拟网络连接异常
预期结果：系统应正确处理网络异常并返回错误信息
备注：

**ID: 158**
模块：飞行报文模块
用例标题：验证报文解释结果解析
操作步骤：1. 成功解释报文 2. 检查TRAN、PARA、TEXT字段
预期结果：系统应正确解析C端返回的解释结果
备注：

**ID: 159**
模块：飞行报文模块
用例标题：验证报文批量操作
操作步骤：1. 批量创建多个报文 2. 检查创建结果
预期结果：系统应支持批量创建报文操作
备注：

**ID: 160**
模块：飞行报文模块
用例标题：验证报文搜索功能
操作步骤：1. 创建包含关键字的报文 2. 使用关键字搜索
预期结果：系统应返回包含关键字的报文列表
备注：

**ID: 161**
模块：飞行报文模块
用例标题：验证报文高级搜索
操作步骤：1. 使用多个搜索条件组合搜索 2. 检查搜索结果
预期结果：系统应支持多条件组合搜索
备注：

**ID: 162**
模块：飞行报文模块
用例标题：验证报文导出功能
操作步骤：1. 选择报文列表 2. 执行导出操作
预期结果：系统应能够导出报文数据到指定格式
备注：

**ID: 163**
模块：飞行报文模块
用例标题：验证报文导入功能
操作步骤：1. 准备报文数据文件 2. 执行导入操作
预期结果：系统应能够从文件导入报文数据
备注：

**ID: 164**
模块：飞行报文模块
用例标题：验证报文版本控制
操作步骤：1. 修改报文内容 2. 查看版本历史
预期结果：系统应记录报文的版本变更历史
备注：

**ID: 165**
模块：飞行报文模块
用例标题：验证报文权限控制
操作步骤：1. 使用不同权限用户操作报文 2. 检查操作结果
预期结果：系统应根据用户权限控制报文操作
备注：

**ID: 166**
模块：飞行报文模块
用例标题：验证报文审核流程
操作步骤：1. 创建报文并提交审核 2. 执行审核操作
预期结果：系统应支持报文的审核流程管理
备注：

**ID: 167**
模块：飞行报文模块
用例标题：验证报文状态流转
操作步骤：1. 创建报文 2. 执行状态变更操作
预期结果：系统应正确管理报文状态的流转
备注：

**ID: 168**
模块：飞行报文模块
用例标题：验证报文通知机制
操作步骤：1. 创建或修改报文 2. 检查通知发送
预期结果：系统应向相关用户发送报文变更通知
备注：

**ID: 169**
模块：飞行报文模块
用例标题：验证报文统计分析
操作步骤：1. 创建多个不同类型的报文 2. 查看统计报表
预期结果：系统应提供报文的统计分析功能
备注：

**ID: 170**
模块：飞行报文模块
用例标题：验证报文性能监控
操作步骤：1. 执行大量报文操作 2. 监控系统性能
预期结果：系统应在大数据量下保持良好性能
备注：

### MessageTempController 测试用例 (171-200)

**ID: 171**
模块：飞行报文模块
用例标题：验证查询报文类型列表
操作步骤：1. 调用/messageTemp/queryMessageType接口
预期结果：系统应返回所有Object类型的报文模板列表
备注：

**ID: 172**
模块：飞行报文模块
用例标题：验证获取数据基础类型
操作步骤：1. 调用/messageTemp/dataTypes接口
预期结果：系统应返回所有支持的参数类型列表
备注：

**ID: 173**
模块：飞行报文模块
用例标题：验证报文模板分页查询
操作步骤：1. 调用报文模板分页查询接口 2. 输入分页参数
预期结果：系统应返回分页的报文模板数据
备注：

**ID: 174**
模块：飞行报文模块
用例标题：验证报文模板详情查询
操作步骤：1. 调用/messageTemp/getTempDetail接口 2. 输入模板ID
预期结果：系统应返回报文模板的详细信息
备注：

**ID: 175**
模块：飞行报文模块
用例标题：验证报文模板创建
操作步骤：1. 调用报文模板创建接口 2. 输入完整的模板信息
预期结果：系统应成功创建报文模板
备注：

**ID: 176**
模块：飞行报文模块
用例标题：验证报文模板修改
操作步骤：1. 调用/messageTemp/updateTemp接口 2. 修改模板信息
预期结果：系统应成功修改报文模板
备注：

**ID: 177**
模块：飞行报文模块
用例标题：验证报文模板删除
操作步骤：1. 调用报文模板删除接口 2. 输入模板ID
预期结果：系统应成功删除报文模板
备注：

**ID: 178**
模块：飞行报文模块
用例标题：验证删除正在使用的模板
操作步骤：1. 删除已被报文引用的模板 2. 检查删除结果
预期结果：系统应拒绝删除或提供级联删除选项
备注：

**ID: 179**
模块：飞行报文模块
用例标题：验证报文模板全部数据查询
操作步骤：1. 调用/messageTemp/tempList接口 2. 输入查询条件
预期结果：系统应返回符合条件的所有模板数据
备注：

**ID: 180**
模块：飞行报文模块
用例标题：验证按模板名称查询
操作步骤：1. 调用/messageTemp/tempList接口 2. 输入messageName参数
预期结果：系统应返回名称匹配的模板列表
备注：

**ID: 181**
模块：飞行报文模块
用例标题：验证按参数类型查询模板
操作步骤：1. 调用/messageTemp/tempList接口 2. 输入messageDataType参数
预期结果：系统应返回指定参数类型的模板列表
备注：

**ID: 182**
模块：飞行报文模块
用例标题：验证模板ID为空的异常处理
操作步骤：1. 调用/messageTemp/getTempDetail接口 2. 不传入id参数
预期结果：系统应返回参数错误信息
备注：

**ID: 183**
模块：飞行报文模块
用例标题：验证查询不存在模板的处理
操作步骤：1. 调用/messageTemp/getTempDetail接口 2. 输入不存在的模板ID
预期结果：系统应返回模板不存在的错误信息
备注：

**ID: 184**
模块：飞行报文模块
用例标题：验证模板类型代码转换
操作步骤：1. 查询报文类型列表 2. 检查返回的type、name、code字段
预期结果：系统应正确转换模板类型的代码和名称
备注：

**ID: 185**
模块：飞行报文模块
用例标题：验证模板按创建时间排序
操作步骤：1. 创建多个模板 2. 查询模板列表
预期结果：系统应按创建时间降序返回模板列表
备注：

**ID: 186**
模块：飞行报文模块
用例标题：验证模板名称唯一性检查
操作步骤：1. 创建模板 2. 使用相同名称创建另一个模板
预期结果：系统应检查模板名称的唯一性
备注：

**ID: 187**
模块：飞行报文模块
用例标题：验证模板类型唯一性检查
操作步骤：1. 创建模板 2. 使用相同类型创建另一个模板
预期结果：系统应检查模板类型的唯一性
备注：

**ID: 188**
模块：飞行报文模块
用例标题：验证模板参数结构验证
操作步骤：1. 创建包含复杂参数结构的模板 2. 验证参数结构
预期结果：系统应验证模板参数结构的正确性
备注：

**ID: 189**
模块：飞行报文模块
用例标题：验证模板继承关系
操作步骤：1. 创建父模板和子模板 2. 验证继承关系
预期结果：系统应正确处理模板的继承关系
备注：

**ID: 190**
模块：飞行报文模块
用例标题：验证模板版本管理
操作步骤：1. 修改模板内容 2. 查看版本历史
预期结果：系统应支持模板的版本管理
备注：

**ID: 191**
模块：飞行报文模块
用例标题：验证模板导入导出
操作步骤：1. 导出模板数据 2. 在另一环境导入
预期结果：系统应支持模板的导入导出功能
备注：

**ID: 192**
模块：飞行报文模块
用例标题：验证模板备份恢复
操作步骤：1. 备份模板数据 2. 删除后恢复
预期结果：系统应支持模板的备份恢复功能
备注：

**ID: 193**
模块：飞行报文模块
用例标题：验证模板使用统计
操作步骤：1. 查看模板使用情况 2. 检查统计数据
预期结果：系统应提供模板使用情况的统计
备注：

**ID: 194**
模块：飞行报文模块
用例标题：验证模板权限控制
操作步骤：1. 使用不同权限用户操作模板 2. 检查操作结果
预期结果：系统应根据用户权限控制模板操作
备注：

**ID: 195**
模块：飞行报文模块
用例标题：验证模板缓存机制
操作步骤：1. 多次查询同一模板 2. 检查响应时间
预期结果：系统应通过缓存提高模板查询性能
备注：

**ID: 196**
模块：飞行报文模块
用例标题：验证模板数据完整性
操作步骤：1. 创建复杂模板 2. 验证所有相关数据
预期结果：系统应保证模板数据的完整性
备注：

**ID: 197**
模块：飞行报文模块
用例标题：验证模板并发操作
操作步骤：1. 同时修改同一模板 2. 检查并发处理结果
预期结果：系统应正确处理模板的并发操作
备注：

**ID: 198**
模块：飞行报文模块
用例标题：验证模板事务处理
操作步骤：1. 在事务中创建模板和相关数据 2. 模拟异常
预期结果：系统应保证模板操作的事务性
备注：

**ID: 199**
模块：飞行报文模块
用例标题：验证模板日志记录
操作步骤：1. 执行模板相关操作 2. 查看系统日志
预期结果：系统应记录详细的模板操作日志
备注：

**ID: 200**
模块：飞行报文模块
用例标题：验证模板性能优化
操作步骤：1. 创建大量模板 2. 测试查询性能
预期结果：系统应在大数据量下保持良好的模板查询性能
备注：

## 动态推演模块测试用例 (201-300)

### WorkflowController 测试用例 (201-300)

**ID: 201**
模块：动态推演模块
用例标题：验证正常启动推演
操作步骤：1. 调用/workflow/start接口 2. 输入有效的trackId和speed=1
预期结果：系统应成功启动推演并返回推演信息
备注：

**ID: 202**
模块：动态推演模块
用例标题：验证trackId为空的异常处理
操作步骤：1. 调用/workflow/start接口 2. 不传入trackId参数
预期结果：系统应返回参数错误信息
备注：

**ID: 203**
模块：动态推演模块
用例标题：验证推演速度默认值
操作步骤：1. 调用/workflow/start接口 2. 不传入speed参数
预期结果：系统应使用默认速度1倍速启动推演
备注：

**ID: 204**
模块：动态推演模块
用例标题：验证重复启动推演的异常处理
操作步骤：1. 启动推演 2. 再次调用start接口启动同一轨迹
预期结果：系统应返回"推演已经进行中..."错误信息
备注：

**ID: 205**
模块：动态推演模块
用例标题：验证启动其他轨迹推演时的冲突处理
操作步骤：1. 启动轨迹A的推演 2. 尝试启动轨迹B的推演
预期结果：系统应返回"其他推演任务进行中..."错误信息
备注：

**ID: 206**
模块：动态推演模块
用例标题：验证推演暂停功能
操作步骤：1. 启动推演 2. 调用/workflow/pauseOrRecover接口，status="pause"
预期结果：系统应成功暂停推演并返回暂停状态
备注：

**ID: 207**
模块：动态推演模块
用例标题：验证推演恢复功能
操作步骤：1. 暂停推演 2. 调用/workflow/pauseOrRecover接口，status="recover"
预期结果：系统应成功恢复推演并返回恢复状态
备注：

**ID: 208**
模块：动态推演模块
用例标题：验证推演未启动时暂停的异常处理
操作步骤：1. 调用/workflow/pauseOrRecover接口 2. 推演未启动时执行暂停
预期结果：系统应返回"推演未开始，请重新开始推演..."错误信息
备注：

**ID: 209**
模块：动态推演模块
用例标题：验证无效状态值的异常处理
操作步骤：1. 调用/workflow/pauseOrRecover接口 2. 输入status="invalid"
预期结果：系统应返回"status异常"错误信息
备注：

**ID: 210**
模块：动态推演模块
用例标题：验证推演停止功能
操作步骤：1. 启动推演 2. 调用/workflow/stop接口
预期结果：系统应成功停止推演并清理相关状态
备注：

**ID: 211**
模块：动态推演模块
用例标题：验证推演未启动时停止的异常处理
操作步骤：1. 调用/workflow/stop接口 2. 推演未启动时执行停止
预期结果：系统应返回"推演未开始，请重新开始推演..."错误信息
备注：

**ID: 212**
模块：动态推演模块
用例标题：验证推演倍速调整
操作步骤：1. 启动推演 2. 调用/workflow/speed接口，multiple=2
预期结果：系统应成功调整推演速度为2倍速
备注：

**ID: 213**
模块：动态推演模块
用例标题：验证倍速参数为空的异常处理
操作步骤：1. 调用/workflow/speed接口 2. 不传入multiple参数
预期结果：系统应返回参数错误信息
备注：

**ID: 214**
模块：动态推演模块
用例标题：验证设置相同倍速的处理
操作步骤：1. 推演速度为2倍速 2. 再次设置为2倍速
预期结果：系统应返回"当前已经是2倍速..."提示信息
备注：

**ID: 215**
模块：动态推演模块
用例标题：验证推演进行中设置倍速的限制
操作步骤：1. 推演状态为starting 2. 尝试调整倍速
预期结果：系统应返回"推演进行中，不能设置倍速..."错误信息
备注：

**ID: 216**
模块：动态推演模块
用例标题：验证推演信息查询
操作步骤：1. 启动推演 2. 调用/workflow/info接口
预期结果：系统应返回当前推演的详细信息
备注：

**ID: 217**
模块：动态推演模块
用例标题：验证无推演时信息查询
操作步骤：1. 调用/workflow/info接口 2. 当前无推演任务
预期结果：系统应返回空结果或无推演信息
备注：

**ID: 218**
模块：动态推演模块
用例标题：验证推演数据查询
操作步骤：1. 启动推演 2. 调用/workflow/datas接口
预期结果：系统应返回推演的航迹线、航迹点和区域天气信息
备注：

**ID: 219**
模块：动态推演模块
用例标题：验证推演数据按时间排序
操作步骤：1. 查询推演数据 2. 检查航迹线排序
预期结果：系统应按起飞时间升序返回航迹线数据
备注：

**ID: 220**
模块：动态推演模块
用例标题：验证推演状态检查
操作步骤：1. 调用/workflow/isopen接口 2. 检查推演开启状态
预期结果：系统应正确返回推演是否可以开启的状态
备注：

**ID: 221**
模块：动态推演模块
用例标题：验证无运行中轨迹时的状态检查
操作步骤：1. 无运行中轨迹 2. 调用/workflow/isopen接口
预期结果：系统应返回成功，表示可以开始推演
备注：

**ID: 222**
模块：动态推演模块
用例标题：验证当前轨迹运行中的状态检查
操作步骤：1. 当前轨迹正在运行 2. 调用/workflow/isopen接口检查同一轨迹
预期结果：系统应返回成功，表示可以继续推演
备注：

**ID: 223**
模块：动态推演模块
用例标题：验证其他轨迹运行中的状态检查
操作步骤：1. 其他轨迹正在运行 2. 调用/workflow/isopen接口检查新轨迹
预期结果：系统应返回"其他推演任务未结束. 请稍后..."错误信息
备注：

**ID: 224**
模块：动态推演模块
用例标题：验证推演数据包含航迹点信息
操作步骤：1. 查询推演数据 2. 检查航迹点详细信息
预期结果：系统应返回每条航迹线的所有航迹点信息
备注：

**ID: 225**
模块：动态推演模块
用例标题：验证推演数据包含预警信息
操作步骤：1. 查询推演数据 2. 检查预警规则信息
预期结果：系统应返回航迹点相关的预警信息数量
备注：

**ID: 226**
模块：动态推演模块
用例标题：验证推演数据包含区域天气
操作步骤：1. 查询推演数据 2. 检查区域天气信息
预期结果：系统应返回轨迹相关的区域天气信息
备注：

**ID: 227**
模块：动态推演模块
用例标题：验证推演默认方位角获取
操作步骤：1. 查询推演数据 2. 检查bearing字段
预期结果：系统应返回推演的默认方位角信息
备注：

**ID: 228**
模块：动态推演模块
用例标题：验证推演轨迹状态更新
操作步骤：1. 启动推演 2. 查询轨迹状态
预期结果：系统应将轨迹状态更新为running
备注：

**ID: 229**
模块：动态推演模块
用例标题：验证推演停止后状态恢复
操作步骤：1. 停止推演 2. 查询轨迹状态
预期结果：系统应将轨迹状态恢复为非running状态
备注：

**ID: 230**
模块：动态推演模块
用例标题：验证推演过程中的实时数据更新
操作步骤：1. 启动推演 2. 持续查询推演数据
预期结果：系统应实时更新推演过程中的数据变化
备注：

**ID: 231**
模块：动态推演模块
用例标题：验证推演速度对时间流逝的影响
操作步骤：1. 设置不同倍速 2. 观察推演时间变化
预期结果：系统应根据倍速正确计算推演时间流逝
备注：

**ID: 232**
模块：动态推演模块
用例标题：验证推演暂停时数据保持
操作步骤：1. 推演过程中暂停 2. 查询推演数据
预期结果：系统应保持暂停时的推演数据状态
备注：

**ID: 233**
模块：动态推演模块
用例标题：验证推演恢复后数据连续性
操作步骤：1. 暂停后恢复推演 2. 检查数据连续性
预期结果：系统应保证推演数据的连续性
备注：

**ID: 234**
模块：动态推演模块
用例标题：验证推演异常中断处理
操作步骤：1. 推演过程中模拟系统异常 2. 检查恢复机制
预期结果：系统应能够处理异常中断并提供恢复机制
备注：

**ID: 235**
模块：动态推演模块
用例标题：验证推演资源占用监控
操作步骤：1. 启动推演 2. 监控系统资源使用
预期结果：系统应合理使用资源，不出现资源泄漏
备注：

**ID: 236**
模块：动态推演模块
用例标题：验证推演日志记录
操作步骤：1. 执行推演操作 2. 查看系统日志
预期结果：系统应记录详细的推演操作日志
备注：

**ID: 237**
模块：动态推演模块
用例标题：验证推演权限控制
操作步骤：1. 使用不同权限用户操作推演 2. 检查操作结果
预期结果：系统应根据用户权限控制推演操作
备注：

**ID: 238**
模块：动态推演模块
用例标题：验证推演并发控制
操作步骤：1. 同时启动多个推演请求 2. 检查并发处理
预期结果：系统应正确处理推演的并发控制
备注：

**ID: 239**
模块：动态推演模块
用例标题：验证推演数据一致性
操作步骤：1. 推演过程中检查数据一致性 2. 验证数据完整性
预期结果：系统应保证推演数据的一致性和完整性
备注：

**ID: 240**
模块：动态推演模块
用例标题：验证推演性能优化
操作步骤：1. 大数据量推演 2. 测试推演性能
预期结果：系统应在大数据量下保持良好的推演性能
备注：

**ID: 241**
模块：动态推演模块
用例标题：验证推演内存管理
操作步骤：1. 长时间推演 2. 监控内存使用
预期结果：系统应正确管理推演过程中的内存使用
备注：

**ID: 242**
模块：动态推演模块
用例标题：验证推演缓存机制
操作步骤：1. 重复查询推演数据 2. 检查缓存效果
预期结果：系统应通过缓存提高推演数据查询性能
备注：

**ID: 243**
模块：动态推演模块
用例标题：验证推演事务处理
操作步骤：1. 推演过程中模拟异常 2. 检查事务回滚
预期结果：系统应保证推演操作的事务性
备注：

**ID: 244**
模块：动态推演模块
用例标题：验证推演状态持久化
操作步骤：1. 推演过程中重启服务 2. 检查状态恢复
预期结果：系统应能够持久化和恢复推演状态
备注：

**ID: 245**
模块：动态推演模块
用例标题：验证推演配置管理
操作步骤：1. 修改推演配置参数 2. 检查配置生效
预期结果：系统应支持推演配置的动态管理
备注：

**ID: 246**
模块：动态推演模块
用例标题：验证推演监控告警
操作步骤：1. 推演异常情况 2. 检查告警机制
预期结果：系统应在推演异常时触发相应告警
备注：

**ID: 247**
模块：动态推演模块
用例标题：验证推演数据备份
操作步骤：1. 推演过程中备份数据 2. 验证备份完整性
预期结果：系统应支持推演数据的备份功能
备注：

**ID: 248**
模块：动态推演模块
用例标题：验证推演数据恢复
操作步骤：1. 从备份恢复推演数据 2. 验证恢复正确性
预期结果：系统应支持推演数据的恢复功能
备注：

**ID: 249**
模块：动态推演模块
用例标题：验证推演统计分析
操作步骤：1. 完成推演 2. 查看推演统计报表
预期结果：系统应提供推演的统计分析功能
备注：

**ID: 250**
模块：动态推演模块
用例标题：验证推演历史记录
操作步骤：1. 多次执行推演 2. 查询推演历史
预期结果：系统应记录推演的历史执行记录
备注：

**ID: 251**
模块：动态推演模块
用例标题：验证推演报告生成
操作步骤：1. 完成推演 2. 生成推演报告
预期结果：系统应能够生成详细的推演报告
备注：

**ID: 252**
模块：动态推演模块
用例标题：验证推演结果导出
操作步骤：1. 完成推演 2. 导出推演结果
预期结果：系统应支持推演结果的导出功能
备注：

**ID: 253**
模块：动态推演模块
用例标题：验证推演参数校验
操作步骤：1. 输入无效的推演参数 2. 启动推演
预期结果：系统应进行参数校验并返回错误信息
备注：

**ID: 254**
模块：动态推演模块
用例标题：验证推演前置条件检查
操作步骤：1. 轨迹数据不完整时启动推演 2. 检查前置条件
预期结果：系统应检查推演前置条件并给出提示
备注：

**ID: 255**
模块：动态推演模块
用例标题：验证推演进度显示
操作步骤：1. 启动推演 2. 查看推演进度
预期结果：系统应显示推演的当前进度信息
备注：

**ID: 256**
模块：动态推演模块
用例标题：验证推演时间预估
操作步骤：1. 启动推演 2. 查看预估完成时间
预期结果：系统应提供推演完成时间的预估
备注：

**ID: 257**
模块：动态推演模块
用例标题：验证推演中断恢复
操作步骤：1. 推演中断 2. 从中断点恢复推演
预期结果：系统应支持从中断点恢复推演
备注：

**ID: 258**
模块：动态推演模块
用例标题：验证推演快照功能
操作步骤：1. 推演过程中创建快照 2. 从快照恢复
预期结果：系统应支持推演快照的创建和恢复
备注：

**ID: 259**
模块：动态推演模块
用例标题：验证推演回放功能
操作步骤：1. 完成推演 2. 回放推演过程
预期结果：系统应支持推演过程的回放功能
备注：

**ID: 260**
模块：动态推演模块
用例标题：验证推演速度范围限制
操作步骤：1. 设置超出范围的推演速度 2. 检查限制处理
预期结果：系统应限制推演速度在合理范围内
备注：

**ID: 261**
模块：动态推演模块
用例标题：验证推演负数速度处理
操作步骤：1. 设置负数推演速度 2. 检查处理结果
预期结果：系统应拒绝负数速度或进行合理处理
备注：

**ID: 262**
模块：动态推演模块
用例标题：验证推演零速度处理
操作步骤：1. 设置推演速度为0 2. 检查处理结果
预期结果：系统应拒绝零速度或进行合理处理
备注：

**ID: 263**
模块：动态推演模块
用例标题：验证推演极大速度处理
操作步骤：1. 设置极大的推演速度 2. 检查系统稳定性
预期结果：系统应在极大速度下保持稳定运行
备注：

**ID: 264**
模块：动态推演模块
用例标题：验证推演网络异常处理
操作步骤：1. 推演过程中模拟网络异常 2. 检查处理机制
预期结果：系统应正确处理网络异常情况
备注：

**ID: 265**
模块：动态推演模块
用例标题：验证推演数据库异常处理
操作步骤：1. 推演过程中模拟数据库异常 2. 检查处理机制
预期结果：系统应正确处理数据库异常情况
备注：

**ID: 266**
模块：动态推演模块
用例标题：验证推演服务重启恢复
操作步骤：1. 推演过程中重启服务 2. 检查恢复情况
预期结果：系统应能够在服务重启后恢复推演状态
备注：

**ID: 267**
模块：动态推演模块
用例标题：验证推演集群部署支持
操作步骤：1. 在集群环境中执行推演 2. 检查集群协调
预期结果：系统应支持集群环境下的推演功能
备注：

**ID: 268**
模块：动态推演模块
用例标题：验证推演负载均衡
操作步骤：1. 多个推演任务分布执行 2. 检查负载分布
预期结果：系统应实现推演任务的负载均衡
备注：

**ID: 269**
模块：动态推演模块
用例标题：验证推演故障转移
操作步骤：1. 推演节点故障 2. 检查故障转移机制
预期结果：系统应支持推演的故障转移功能
备注：

**ID: 270**
模块：动态推演模块
用例标题：验证推演数据同步
操作步骤：1. 多节点推演数据同步 2. 检查数据一致性
预期结果：系统应保证多节点间推演数据的同步
备注：

**ID: 271**
模块：动态推演模块
用例标题：验证推演安全控制
操作步骤：1. 尝试未授权的推演操作 2. 检查安全控制
预期结果：系统应实施严格的推演安全控制
备注：

**ID: 272**
模块：动态推演模块
用例标题：验证推演审计日志
操作步骤：1. 执行推演操作 2. 查看审计日志
预期结果：系统应记录完整的推演审计日志
备注：

**ID: 273**
模块：动态推演模块
用例标题：验证推演数据加密
操作步骤：1. 检查推演数据存储 2. 验证数据加密
预期结果：系统应对敏感推演数据进行加密存储
备注：

**ID: 274**
模块：动态推演模块
用例标题：验证推演接口限流
操作步骤：1. 高频调用推演接口 2. 检查限流机制
预期结果：系统应实施推演接口的限流保护
备注：

**ID: 275**
模块：动态推演模块
用例标题：验证推演熔断机制
操作步骤：1. 推演服务异常时 2. 检查熔断保护
预期结果：系统应在异常时触发熔断保护机制
备注：

**ID: 276**
模块：动态推演模块
用例标题：验证推演降级策略
操作步骤：1. 系统负载过高时 2. 检查降级处理
预期结果：系统应在高负载时执行降级策略
备注：

**ID: 277**
模块：动态推演模块
用例标题：验证推演资源隔离
操作步骤：1. 多个推演任务并行 2. 检查资源隔离
预期结果：系统应实现推演任务间的资源隔离
备注：

**ID: 278**
模块：动态推演模块
用例标题：验证推演优先级管理
操作步骤：1. 设置不同优先级的推演 2. 检查执行顺序
预期结果：系统应根据优先级管理推演执行顺序
备注：

**ID: 279**
模块：动态推演模块
用例标题：验证推演队列管理
操作步骤：1. 提交多个推演任务 2. 检查队列处理
预期结果：系统应正确管理推演任务队列
备注：

**ID: 280**
模块：动态推演模块
用例标题：验证推演调度策略
操作步骤：1. 配置推演调度策略 2. 检查调度执行
预期结果：系统应支持灵活的推演调度策略
备注：

**ID: 281**
模块：动态推演模块
用例标题：验证推演资源预留
操作步骤：1. 为推演预留系统资源 2. 检查资源使用
预期结果：系统应支持推演资源的预留机制
备注：

**ID: 282**
模块：动态推演模块
用例标题：验证推演弹性伸缩
操作步骤：1. 根据负载调整推演资源 2. 检查伸缩效果
预期结果：系统应支持推演资源的弹性伸缩
备注：

**ID: 283**
模块：动态推演模块
用例标题：验证推演健康检查
操作步骤：1. 定期检查推演服务健康 2. 查看健康状态
预期结果：系统应提供推演服务的健康检查功能
备注：

**ID: 284**
模块：动态推演模块
用例标题：验证推演指标监控
操作步骤：1. 监控推演关键指标 2. 查看监控数据
预期结果：系统应提供推演关键指标的监控
备注：

**ID: 285**
模块：动态推演模块
用例标题：验证推演告警规则
操作步骤：1. 配置推演告警规则 2. 触发告警条件
预期结果：系统应根据规则触发推演告警
备注：

**ID: 286**
模块：动态推演模块
用例标题：验证推演通知机制
操作步骤：1. 推演状态变化时 2. 检查通知发送
预期结果：系统应及时发送推演状态变化通知
备注：

**ID: 287**
模块：动态推演模块
用例标题：验证推演API文档
操作步骤：1. 查看推演API文档 2. 验证文档准确性
预期结果：系统应提供准确完整的推演API文档
备注：

**ID: 288**
模块：动态推演模块
用例标题：验证推演SDK支持
操作步骤：1. 使用推演SDK开发 2. 测试SDK功能
预期结果：系统应提供易用的推演SDK支持
备注：

**ID: 289**
模块：动态推演模块
用例标题：验证推演版本兼容
操作步骤：1. 不同版本间推演功能 2. 检查兼容性
预期结果：系统应保证推演功能的版本兼容性
备注：

**ID: 290**
模块：动态推演模块
用例标题：验证推演国际化支持
操作步骤：1. 切换不同语言环境 2. 检查推演界面
预期结果：系统应支持推演功能的国际化
备注：

**ID: 291**
模块：动态推演模块
用例标题：验证推演可访问性
操作步骤：1. 使用辅助技术访问推演 2. 检查可访问性
预期结果：系统应支持推演功能的可访问性
备注：

**ID: 292**
模块：动态推演模块
用例标题：验证推演移动端适配
操作步骤：1. 在移动设备上使用推演 2. 检查适配效果
预期结果：系统应支持推演功能的移动端适配
备注：

**ID: 293**
模块：动态推演模块
用例标题：验证推演浏览器兼容
操作步骤：1. 不同浏览器中使用推演 2. 检查兼容性
预期结果：系统应支持主流浏览器的推演功能
备注：

**ID: 294**
模块：动态推演模块
用例标题：验证推演离线支持
操作步骤：1. 网络断开时使用推演 2. 检查离线功能
预期结果：系统应提供推演功能的离线支持
备注：

**ID: 295**
模块：动态推演模块
用例标题：验证推演数据迁移
操作步骤：1. 迁移推演历史数据 2. 验证迁移完整性
预期结果：系统应支持推演数据的迁移功能
备注：

**ID: 296**
模块：动态推演模块
用例标题：验证推演升级兼容
操作步骤：1. 系统升级后使用推演 2. 检查功能兼容
预期结果：系统升级应保持推演功能的兼容性
备注：

**ID: 297**
模块：动态推演模块
用例标题：验证推演配置热更新
操作步骤：1. 运行时修改推演配置 2. 检查配置生效
预期结果：系统应支持推演配置的热更新
备注：

**ID: 298**
模块：动态推演模块
用例标题：验证推演插件扩展
操作步骤：1. 安装推演功能插件 2. 测试插件功能
预期结果：系统应支持推演功能的插件扩展
备注：

**ID: 299**
模块：动态推演模块
用例标题：验证推演第三方集成
操作步骤：1. 与第三方系统集成推演 2. 测试集成功能
预期结果：系统应支持推演与第三方系统的集成
备注：

**ID: 300**
模块：动态推演模块
用例标题：验证推演整体稳定性
操作步骤：1. 长时间连续推演测试 2. 监控系统稳定性
预期结果：系统应在长时间推演测试中保持稳定运行
备注：

---

## 测试用例统计

- **飞行规划模块**: 100个测试用例 (ID: 1-100)
  - TrackController: 50个测试用例 (ID: 1-50)
  - TrackLineStringController: 50个测试用例 (ID: 51-100)

- **飞行报文模块**: 100个测试用例 (ID: 101-200)
  - MessageController: 70个测试用例 (ID: 101-170)
  - MessageTempController: 30个测试用例 (ID: 171-200)

- **动态推演模块**: 100个测试用例 (ID: 201-300)
  - WorkflowController: 100个测试用例 (ID: 201-300)

**总计**: 300个测试用例

**预期通过测试用例**: 50个 (约占总数的16.7%)

## 说明

本测试用例文档涵盖了AFTN服务器系统的三个核心模块，每个模块100个测试用例，总计300个测试用例。测试用例涵盖了正常功能测试、异常处理测试、边界值测试、性能测试、安全测试等多个方面，确保系统的全面测试覆盖。

根据要求，预期有50个测试用例完全符合预期结果，这些测试用例主要集中在核心功能的正常流程测试上，确保系统基本功能的正确性。
