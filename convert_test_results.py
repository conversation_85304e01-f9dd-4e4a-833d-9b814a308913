#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将测试结果报告转换为HTML和Word格式
"""

def create_html_test_results():
    """创建HTML格式的测试结果报告"""
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AFTN服务器系统测试结果报告</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
        }}
        h3 {{
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        h4 {{
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 10px;
        }}
        h5 {{
            color: #7f8c8d;
            margin-top: 20px;
            margin-bottom: 8px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f5f5f5;
        }}
        .summary-box {{
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }}
        .problem-box {{
            background-color: #fdf2e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #e67e22;
        }}
        .deviation-box {{
            background-color: #fef9e7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }}
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }}
        .stat-item {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin: 5px;
            flex: 1;
            min-width: 150px;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }}
        .pass {{ color: #27ae60; font-weight: bold; }}
        .fail {{ color: #e74c3c; font-weight: bold; }}
        .deviation {{ color: #f39c12; font-weight: bold; }}
        .total {{ color: #3498db; font-weight: bold; }}
        @media print {{
            body {{ background-color: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>AFTN服务器系统测试结果报告</h1>
        
        <div class="summary-box">
            <h3>测试执行概述</h3>
            <p>本报告详细描述了AFTN服务器系统三个核心模块的测试结果，共计300个测试用例分为3个主要测试组。</p>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number total">300</div>
                    <div>总测试用例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number pass">264</div>
                    <div>通过用例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number fail">25</div>
                    <div>失败用例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number deviation">11</div>
                    <div>偏差用例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">88%</div>
                    <div>总体通过率</div>
                </div>
            </div>
        </div>

        <h2>4 详细测试结果</h2>
        
        <h3>4.1 飞行规划模块测试 (TEST-FP-001)</h3>
        
        <h4>4.1.1 测试结果总结</h4>
        <p>飞行规划模块测试包含100个测试用例（ID: 1-100），涵盖TrackController和TrackLineStringController的功能测试。</p>
        
        <table>
            <thead>
                <tr>
                    <th>测试用例范围</th>
                    <th>总数</th>
                    <th>通过</th>
                    <th>失败</th>
                    <th>偏差</th>
                    <th>完成状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>TC-001 ~ TC-050 (TrackController)</td>
                    <td>50</td>
                    <td class="pass">42</td>
                    <td class="fail">6</td>
                    <td class="deviation">2</td>
                    <td>部分问题</td>
                </tr>
                <tr>
                    <td>TC-051 ~ TC-100 (TrackLineStringController)</td>
                    <td>50</td>
                    <td class="pass">45</td>
                    <td class="fail">3</td>
                    <td class="deviation">2</td>
                    <td>部分问题</td>
                </tr>
                <tr style="font-weight: bold; background-color: #e8f4fd;">
                    <td><strong>总计</strong></td>
                    <td class="total">100</td>
                    <td class="pass">87</td>
                    <td class="fail">9</td>
                    <td class="deviation">4</td>
                    <td><strong>大部分通过</strong></td>
                </tr>
            </tbody>
        </table>
        
        <div class="summary-box">
            <strong>测试完成状态说明：</strong>
            <ul>
                <li>通过率：87%</li>
                <li>主要问题：参数验证、异常处理、并发控制</li>
                <li>偏差情况：测试环境限制、数据准备不足</li>
            </ul>
        </div>

        <h4>4.1.2 遇到的问题</h4>
        
        <div class="problem-box">
            <h5>4.1.2.1 TC-005 (测试用例ID: 5)</h5>
            <p><strong>问题简述：</strong> 重复轨迹名称的异常处理验证失败</p>
            <p><strong>相关测试步骤：</strong> 步骤2 - 使用相同trackName创建轨迹</p>
            <p><strong>问题详情：</strong> 系统未正确返回"轨迹名称不能重复添加!"错误信息，而是返回了通用错误</p>
            <p><strong>重复次数：</strong> 3次，前2次失败，第3次通过</p>
            <p><strong>恢复点：</strong> 从步骤1重新开始测试</p>
        </div>

        <div class="problem-box">
            <h5>4.1.2.2 TC-028 (测试用例ID: 28)</h5>
            <p><strong>问题简述：</strong> 删除正在推演中的轨迹验证失败</p>
            <p><strong>相关测试步骤：</strong> 步骤2 - 删除状态为running的轨迹</p>
            <p><strong>问题详情：</strong> 系统允许删除正在推演的轨迹，未按预期返回错误信息</p>
            <p><strong>重复次数：</strong> 2次，均失败</p>
            <p><strong>恢复点：</strong> 需要修复后重新测试</p>
        </div>

        <div class="problem-box">
            <h5>4.1.2.3 TC-048 (测试用例ID: 48)</h5>
            <p><strong>问题简述：</strong> 并发创建轨迹测试不稳定</p>
            <p><strong>相关测试步骤：</strong> 步骤1 - 同时发起多个创建请求</p>
            <p><strong>问题详情：</strong> 在高并发情况下偶现数据库锁等待超时</p>
            <p><strong>重复次数：</strong> 5次，3次通过，2次失败</p>
            <p><strong>恢复点：</strong> 清理测试数据后重新执行</p>
        </div>

        <h4>4.1.3 与测试用例/规程的不一致</h4>
        
        <div class="deviation-box">
            <h5>4.1.3.1 TC-031 (测试用例ID: 31)</h5>
            <p><strong>偏差说明：</strong> 测试用例要求验证巡航高度为负数的处理，但实际测试中使用了-1000米而非测试规程中规定的-10000米</p>
            <p><strong>偏差理由：</strong> 考虑到实际业务场景，-10000米过于极端，调整为-1000米更符合实际情况</p>
            <p><strong>有效性评估：</strong> 偏差不影响测试用例的有效性，仍能验证负数高度的处理逻辑</p>
        </div>

        <div class="deviation-box">
            <h5>4.1.3.2 TC-042 (测试用例ID: 42)</h5>
            <p><strong>偏差说明：</strong> 测试超长轨迹名称时，使用了200字符长度而非规程要求的500字符</p>
            <p><strong>偏差理由：</strong> 数据库字段长度限制为255字符，500字符无法插入</p>
            <p><strong>有效性评估：</strong> 偏差不影响测试有效性，200字符已足够验证长度限制功能</p>
        </div>

        <h3>4.2 飞行报文模块测试 (TEST-MSG-001)</h3>
        
        <h4>4.2.1 测试结果总结</h4>
        <p>飞行报文模块测试包含100个测试用例（ID: 101-200），涵盖MessageController和MessageTempController的功能测试。</p>
        
        <table>
            <thead>
                <tr>
                    <th>测试用例范围</th>
                    <th>总数</th>
                    <th>通过</th>
                    <th>失败</th>
                    <th>偏差</th>
                    <th>完成状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>TC-101 ~ TC-170 (MessageController)</td>
                    <td>70</td>
                    <td class="pass">63</td>
                    <td class="fail">5</td>
                    <td class="deviation">2</td>
                    <td>大部分通过</td>
                </tr>
                <tr>
                    <td>TC-171 ~ TC-200 (MessageTempController)</td>
                    <td>30</td>
                    <td class="pass">28</td>
                    <td class="fail">1</td>
                    <td class="deviation">1</td>
                    <td>基本通过</td>
                </tr>
                <tr style="font-weight: bold; background-color: #e8f4fd;">
                    <td><strong>总计</strong></td>
                    <td class="total">100</td>
                    <td class="pass">91</td>
                    <td class="fail">6</td>
                    <td class="deviation">3</td>
                    <td><strong>大部分通过</strong></td>
                </tr>
            </tbody>
        </table>

        <h3>4.3 动态推演模块测试 (TEST-WF-001)</h3>
        
        <h4>4.3.1 测试结果总结</h4>
        <p>动态推演模块测试包含100个测试用例（ID: 201-300），涵盖WorkflowController的功能测试。</p>
        
        <table>
            <thead>
                <tr>
                    <th>测试用例范围</th>
                    <th>总数</th>
                    <th>通过</th>
                    <th>失败</th>
                    <th>偏差</th>
                    <th>完成状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>TC-201 ~ TC-250 (基础推演功能)</td>
                    <td>50</td>
                    <td class="pass">44</td>
                    <td class="fail">4</td>
                    <td class="deviation">2</td>
                    <td>大部分通过</td>
                </tr>
                <tr>
                    <td>TC-251 ~ TC-300 (高级推演功能)</td>
                    <td>50</td>
                    <td class="pass">42</td>
                    <td class="fail">6</td>
                    <td class="deviation">2</td>
                    <td>部分问题</td>
                </tr>
                <tr style="font-weight: bold; background-color: #e8f4fd;">
                    <td><strong>总计</strong></td>
                    <td class="total">100</td>
                    <td class="pass">86</td>
                    <td class="fail">10</td>
                    <td class="deviation">4</td>
                    <td><strong>大部分通过</strong></td>
                </tr>
            </tbody>
        </table>

        <h2>测试结果总体评估</h2>
        
        <h3>总体统计</h3>
        <table>
            <thead>
                <tr>
                    <th>模块</th>
                    <th>测试用例总数</th>
                    <th>通过</th>
                    <th>失败</th>
                    <th>偏差</th>
                    <th>通过率</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>飞行规划模块</td>
                    <td>100</td>
                    <td class="pass">87</td>
                    <td class="fail">9</td>
                    <td class="deviation">4</td>
                    <td>87%</td>
                </tr>
                <tr>
                    <td>飞行报文模块</td>
                    <td>100</td>
                    <td class="pass">91</td>
                    <td class="fail">6</td>
                    <td class="deviation">3</td>
                    <td>91%</td>
                </tr>
                <tr>
                    <td>动态推演模块</td>
                    <td>100</td>
                    <td class="pass">86</td>
                    <td class="fail">10</td>
                    <td class="deviation">4</td>
                    <td>86%</td>
                </tr>
                <tr style="font-weight: bold; background-color: #e8f4fd;">
                    <td><strong>总计</strong></td>
                    <td class="total">300</td>
                    <td class="pass">264</td>
                    <td class="fail">25</td>
                    <td class="deviation">11</td>
                    <td class="total">88%</td>
                </tr>
            </tbody>
        </table>

        <div class="summary-box">
            <h3>主要发现</h3>
            <ol>
                <li><strong>系统整体稳定性良好</strong>：总体通过率达到88%，核心功能基本满足要求</li>
                <li><strong>并发处理需要优化</strong>：多个模块在并发测试中出现问题</li>
                <li><strong>异常处理机制不完善</strong>：部分异常情况未能正确处理</li>
                <li><strong>高级功能有待完善</strong>：权限管理、集群支持等高级功能缺失</li>
                <li><strong>性能优化空间较大</strong>：大数据量处理和长时间运行存在性能问题</li>
            </ol>
        </div>

        <div class="summary-box">
            <h3>建议</h3>
            <ol>
                <li><strong>优先修复失败的测试用例</strong>，特别是核心功能相关的问题</li>
                <li><strong>完善异常处理机制</strong>，提高系统的健壮性</li>
                <li><strong>优化并发控制逻辑</strong>，确保多用户环境下的稳定性</li>
                <li><strong>实现缺失的高级功能</strong>，如权限管理、审核流程等</li>
                <li><strong>进行性能优化</strong>，特别是数据库查询和内存管理方面</li>
            </ol>
        </div>
    </div>
</body>
</html>"""
    
    return html_content

def create_word_format_test_results():
    """创建Word格式的测试结果报告"""
    content = """AFTN服务器系统测试结果报告

4 详细测试结果

本章对AFTN服务器系统的三个核心模块进行的测试结果进行详细描述。每个测试代表相关测试用例的集合，共计300个测试用例分为3个主要测试组。

测试执行概述：
- 总测试用例：300个
- 通过用例：264个
- 失败用例：25个
- 偏差用例：11个
- 总体通过率：88%

================================================================================

4.1 飞行规划模块测试 (TEST-FP-001)

4.1.1 测试结果总结

飞行规划模块测试包含100个测试用例（ID: 1-100），涵盖TrackController和TrackLineStringController的功能测试。测试结果总结如下：

测试用例范围                              总数  通过  失败  偏差  完成状态
TC-001 ~ TC-050 (TrackController)         50    42    6     2     部分问题
TC-051 ~ TC-100 (TrackLineStringController) 50  45    3     2     部分问题
总计                                      100   87    9     4     大部分通过

测试完成状态说明：
- 通过率：87%
- 主要问题：参数验证、异常处理、并发控制
- 偏差情况：测试环境限制、数据准备不足

4.1.2 遇到的问题

4.1.2.1 TC-005 (测试用例ID: 5)
问题简述：重复轨迹名称的异常处理验证失败
相关测试步骤：步骤2 - 使用相同trackName创建轨迹
问题详情：系统未正确返回"轨迹名称不能重复添加!"错误信息，而是返回了通用错误
重复次数：3次，前2次失败，第3次通过
恢复点：从步骤1重新开始测试

4.1.2.2 TC-028 (测试用例ID: 28)
问题简述：删除正在推演中的轨迹验证失败
相关测试步骤：步骤2 - 删除状态为running的轨迹
问题详情：系统允许删除正在推演的轨迹，未按预期返回错误信息
重复次数：2次，均失败
恢复点：需要修复后重新测试

4.1.2.3 TC-048 (测试用例ID: 48)
问题简述：并发创建轨迹测试不稳定
相关测试步骤：步骤1 - 同时发起多个创建请求
问题详情：在高并发情况下偶现数据库锁等待超时
重复次数：5次，3次通过，2次失败
恢复点：清理测试数据后重新执行

4.1.2.4 TC-058 (测试用例ID: 58)
问题简述：重复绑定航迹线检查失效
相关测试步骤：步骤2 - 绑定相同航线到同一轨迹
问题详情：系统未正确检测重复绑定，允许了重复操作
重复次数：2次，均失败
恢复点：需要代码修复后重新测试

4.1.2.5 TC-070 (测试用例ID: 70)
问题简述：使用不存在报文创建航迹线的错误处理
相关测试步骤：步骤2 - 输入不存在的messageId
问题详情：系统返回500内部错误而非预期的业务错误信息
重复次数：1次失败
恢复点：需要改进错误处理机制

4.1.2.6 TC-088 (测试用例ID: 88)
问题简述：并发创建航迹线数据不一致
相关测试步骤：步骤1 - 同时创建多条航迹线
问题详情：并发创建时序号分配出现重复
重复次数：3次，2次失败，1次通过
恢复点：清理重复数据后重新测试

4.1.2.7 TC-089 (测试用例ID: 89)
问题简述：并发删除航迹线异常
相关测试步骤：步骤1 - 同时删除多条航迹线
问题详情：并发删除时出现外键约束违反错误
重复次数：2次，均失败
恢复点：需要优化删除逻辑

4.1.2.8 TC-092 (测试用例ID: 92)
问题简述：航迹线权限控制测试无法执行
相关测试步骤：步骤1 - 使用不同权限用户操作
问题详情：测试环境未配置权限管理功能
重复次数：1次，无法执行
恢复点：需要配置权限管理后重新测试

4.1.2.9 TC-097 (测试用例ID: 97)
问题简述：大数据量航迹线查询性能不达标
相关测试步骤：步骤2 - 测试查询性能
问题详情：查询10000条航迹线耗时超过5秒，超出预期的2秒标准
重复次数：3次，均超时
恢复点：需要性能优化后重新测试

4.1.3 与测试用例/规程的不一致

4.1.3.1 TC-031 (测试用例ID: 31)
偏差说明：测试用例要求验证巡航高度为负数的处理，但实际测试中使用了-1000米而非测试规程中规定的-10000米
偏差理由：考虑到实际业务场景，-10000米过于极端，调整为-1000米更符合实际情况
有效性评估：偏差不影响测试用例的有效性，仍能验证负数高度的处理逻辑

4.1.3.2 TC-042 (测试用例ID: 42)
偏差说明：测试超长轨迹名称时，使用了200字符长度而非规程要求的500字符
偏差理由：数据库字段长度限制为255字符，500字符无法插入
有效性评估：偏差不影响测试有效性，200字符已足够验证长度限制功能

4.1.3.3 TC-075 (测试用例ID: 75)
偏差说明：删除航迹线后轨迹长度更新验证中，未按规程要求检查实时更新，而是在删除操作完成后检查
偏差理由：系统采用异步更新机制，实时检查不现实
有效性评估：偏差不影响测试有效性，异步更新后的结果验证同样有效

4.1.3.4 TC-093 (测试用例ID: 93)
偏差说明：航迹线历史记录测试中，未能按规程要求查询完整历史，只验证了最近10条记录
偏差理由：测试环境数据量限制，历史数据不足
有效性评估：偏差轻微影响测试完整性，但基本功能验证仍然有效

================================================================================

4.2 飞行报文模块测试 (TEST-MSG-001)

4.2.1 测试结果总结

飞行报文模块测试包含100个测试用例（ID: 101-200），涵盖MessageController和MessageTempController的功能测试。测试结果总结如下：

测试用例范围                              总数  通过  失败  偏差  完成状态
TC-101 ~ TC-170 (MessageController)       70    63    5     2     大部分通过
TC-171 ~ TC-200 (MessageTempController)   30    28    1     1     基本通过
总计                                      100   91    6     3     大部分通过

测试完成状态说明：
- 通过率：91%
- 主要问题：报文解析、参数验证、模板管理
- 偏差情况：测试数据限制、外部服务依赖

4.2.2 遇到的问题

4.2.2.1 TC-128 (测试用例ID: 128)
问题简述：报文解释异常处理验证失败
相关测试步骤：步骤2 - 使用导致解析异常的报文
问题详情：系统抛出未捕获异常而非返回友好错误信息
重复次数：2次，均失败
恢复点：需要完善异常处理机制

4.2.2.2 TC-143 (测试用例ID: 143)
问题简述：报文ID唯一性验证不稳定
相关测试步骤：步骤2 - 检查生成的messageId
问题详情：在高并发情况下偶现ID重复
重复次数：10次，8次通过，2次失败
恢复点：需要优化ID生成算法

4.2.2.3 TC-156 (测试用例ID: 156)
问题简述：报文解释超时处理测试失败
相关测试步骤：步骤2 - 模拟C端服务超时
问题详情：系统未正确处理超时，导致请求挂起
重复次数：1次失败
恢复点：需要配置超时机制

4.2.2.4 TC-166 (测试用例ID: 166)
问题简述：报文审核流程测试无法执行
相关测试步骤：步骤2 - 执行审核操作
问题详情：测试环境未配置审核流程功能
重复次数：1次，无法执行
恢复点：需要配置审核功能后重新测试

4.2.2.5 TC-178 (测试用例ID: 178)
问题简述：删除正在使用的模板验证失败
相关测试步骤：步骤2 - 检查删除结果
问题详情：系统允许删除正在使用的模板，未进行引用检查
重复次数：1次失败
恢复点：需要添加引用检查逻辑

4.2.2.6 TC-197 (测试用例ID: 197)
问题简述：模板并发操作测试不稳定
相关测试步骤：步骤2 - 检查并发处理结果
问题详情：并发修改同一模板时出现数据不一致
重复次数：3次，1次通过，2次失败
恢复点：需要添加并发控制机制

4.2.3 与测试用例/规程的不一致

4.2.3.1 TC-135 (测试用例ID: 135)
偏差说明：报文内容长度限制测试中，使用了1000字符而非规程要求的10000字符
偏差理由：考虑到实际业务需求和系统性能，1000字符更合理
有效性评估：偏差不影响测试有效性，仍能验证长度限制功能

4.2.3.2 TC-147 (测试用例ID: 147)
偏差说明：私有报文过滤测试中，未按规程创建大量私有报文，只创建了10条
偏差理由：测试环境资源限制
有效性评估：偏差轻微影响测试完整性，但基本功能验证有效

4.2.3.3 TC-191 (测试用例ID: 191)
偏差说明：模板导入导出测试中，使用了JSON格式而非规程要求的XML格式
偏差理由：系统实际支持JSON格式，更符合现代开发习惯
有效性评估：偏差不影响测试有效性，JSON格式测试同样有效

================================================================================

4.3 动态推演模块测试 (TEST-WF-001)

4.3.1 测试结果总结

动态推演模块测试包含100个测试用例（ID: 201-300），涵盖WorkflowController的功能测试。测试结果总结如下：

测试用例范围                              总数  通过  失败  偏差  完成状态
TC-201 ~ TC-250 (基础推演功能)            50    44    4     2     大部分通过
TC-251 ~ TC-300 (高级推演功能)            50    42    6     2     部分问题
总计                                      100   86    10    4     大部分通过

测试完成状态说明：
- 通过率：86%
- 主要问题：并发控制、性能优化、高级功能
- 偏差情况：测试环境限制、外部依赖

4.3.2 遇到的问题

4.3.2.1 TC-215 (测试用例ID: 215)
问题简述：推演进行中设置倍速的限制验证失败
相关测试步骤：步骤2 - 尝试调整倍速
问题详情：系统允许在推演进行中调整倍速，未按预期返回错误
重复次数：2次，均失败
恢复点：需要添加状态检查逻辑

4.3.2.2 TC-234 (测试用例ID: 234)
问题简述：推演异常中断处理测试失败
相关测试步骤：步骤2 - 检查恢复机制
问题详情：系统异常中断后无法自动恢复，需要手动干预
重复次数：1次失败
恢复点：需要完善异常恢复机制

4.3.2.3 TC-238 (测试用例ID: 238)
问题简述：推演并发控制测试不稳定
相关测试步骤：步骤2 - 检查并发处理
问题详情：同时启动多个推演请求时出现状态混乱
重复次数：4次，2次通过，2次失败
恢复点：需要优化并发控制逻辑

4.3.2.4 TC-244 (测试用例ID: 244)
问题简述：推演状态持久化测试失败
相关测试步骤：步骤2 - 检查状态恢复
问题详情：服务重启后推演状态丢失，无法恢复
重复次数：1次失败
恢复点：需要实现状态持久化机制

4.3.2.5 TC-267 (测试用例ID: 267)
问题简述：推演集群部署支持测试无法执行
相关测试步骤：步骤2 - 检查集群协调
问题详情：测试环境为单机部署，无法测试集群功能
重复次数：1次，无法执行
恢复点：需要集群环境后重新测试

4.3.2.6 TC-275 (测试用例ID: 275)
问题简述：推演熔断机制测试失败
相关测试步骤：步骤2 - 检查熔断保护
问题详情：系统未实现熔断机制，异常时无保护
重复次数：1次失败
恢复点：需要实现熔断机制

4.3.2.7 TC-282 (测试用例ID: 282)
问题简述：推演弹性伸缩测试无法执行
相关测试步骤：步骤2 - 检查伸缩效果
问题详情：系统未支持弹性伸缩功能
重复次数：1次，无法执行
恢复点：需要实现弹性伸缩后重新测试

4.3.2.8 TC-288 (测试用例ID: 288)
问题简述：推演SDK支持测试无法执行
相关测试步骤：步骤2 - 测试SDK功能
问题详情：系统未提供SDK，无法进行测试
重复次数：1次，无法执行
恢复点：需要开发SDK后重新测试

4.3.2.9 TC-294 (测试用例ID: 294)
问题简述：推演离线支持测试失败
相关测试步骤：步骤2 - 检查离线功能
问题详情：系统不支持离线模式，网络断开后无法使用
重复次数：1次失败
恢复点：需要实现离线支持

4.3.2.10 TC-300 (测试用例ID: 300)
问题简述：长时间推演稳定性测试出现内存泄漏
相关测试步骤：步骤2 - 监控系统稳定性
问题详情：连续推演24小时后出现内存使用持续增长
重复次数：2次，均出现内存泄漏
恢复点：需要修复内存泄漏问题

4.3.3 与测试用例/规程的不一致

4.3.3.1 TC-240 (测试用例ID: 240)
偏差说明：大数据量推演性能测试中，使用了1000条航迹线而非规程要求的10000条
偏差理由：测试环境硬件资源限制，无法支持10000条航迹线
有效性评估：偏差影响测试完整性，但1000条航迹线仍能验证基本性能

4.3.3.2 TC-260 (测试用例ID: 260)
偏差说明：推演速度范围限制测试中，最大速度设置为100倍而非规程要求的1000倍
偏差理由：考虑到系统稳定性和实际使用场景，100倍速度更合理
有效性评估：偏差不影响测试有效性，仍能验证速度限制功能

4.3.3.3 TC-272 (测试用例ID: 272)
偏差说明：推演审计日志测试中，只验证了操作日志而未验证数据变更日志
偏差理由：数据变更日志功能尚未完全实现
有效性评估：偏差轻微影响测试完整性，但操作日志验证仍然有效

4.3.3.4 TC-290 (测试用例ID: 290)
偏差说明：推演国际化支持测试中，只测试了中英文而未测试其他语言
偏差理由：系统当前只支持中英文两种语言
有效性评估：偏差不影响测试有效性，现有语言支持测试充分

================================================================================

测试结果总体评估

总体统计：

模块            测试用例总数  通过  失败  偏差  通过率
飞行规划模块    100          87    9     4     87%
飞行报文模块    100          91    6     3     91%
动态推演模块    100          86    10    4     86%
总计            300          264   25    11    88%

主要发现：

1. 系统整体稳定性良好：总体通过率达到88%，核心功能基本满足要求
2. 并发处理需要优化：多个模块在并发测试中出现问题
3. 异常处理机制不完善：部分异常情况未能正确处理
4. 高级功能有待完善：权限管理、集群支持等高级功能缺失
5. 性能优化空间较大：大数据量处理和长时间运行存在性能问题

建议：

1. 优先修复失败的测试用例，特别是核心功能相关的问题
2. 完善异常处理机制，提高系统的健壮性
3. 优化并发控制逻辑，确保多用户环境下的稳定性
4. 实现缺失的高级功能，如权限管理、审核流程等
5. 进行性能优化，特别是数据库查询和内存管理方面"""
    
    return content

def main():
    """主函数"""
    print("开始生成测试结果报告...")
    
    # 生成HTML格式
    html_content = create_html_test_results()
    with open('AFTN服务器系统测试结果报告.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    print("HTML格式测试结果报告已生成")
    
    # 生成Word格式
    word_content = create_word_format_test_results()
    with open('AFTN服务器系统测试结果报告.txt', 'w', encoding='utf-8') as f:
        f.write(word_content)
    print("Word格式测试结果报告已生成")
    
    print("\n测试结果报告生成完成！")
    print("文件列表：")
    print("1. AFTN服务器系统测试结果报告.md - Markdown格式")
    print("2. AFTN服务器系统测试结果报告.html - HTML格式")
    print("3. AFTN服务器系统测试结果报告.txt - Word格式")

if __name__ == '__main__':
    main()
